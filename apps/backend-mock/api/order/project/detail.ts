import { fakerZH_CN as faker } from '@faker-js/faker';

function generateFullAddress() {
  const province = faker.location.state();
  const city = faker.location.city();
  const district = faker.location.county();

  return `${province} ${city} ${district}`;
}
function generateMockData() {
  return {
    code: faker.string.uuid(),
    name: faker.commerce.productName(),
    auditStatus: faker.helpers.arrayElement(['00', '10', '20']),
    remark: faker.lorem.lines(),
    reason: faker.lorem.lines(),
    status: faker.helpers.arrayElement(['00', '10', '20', '30', '40']),
    mode: faker.helpers.arrayElement(['10', '20', '30']),
    deliveryMethod: faker.helpers.arrayElement(['01', '02', '03']),
    tradeExecutionEnterpriseName: faker.company.name(),
    upEnterpriseName: faker.company.name(),
    downEnterpriseName: faker.company.name(),
    fundingPartyName: faker.company.name(),
    guarantorName: faker.company.name(),
    projectType: faker.helpers.arrayElement(['类型A', '类型B', '类型C']),
    businessDate: faker.date.past().toISOString(),
    createDepartment: faker.commerce.department(),
    createBy: faker.person.fullName(),
    createTime: faker.date.recent().toISOString(),
    planStartDate: faker.date.recent().toISOString(),
    planCompletionDate: faker.date.recent().toISOString(),
    planDays: faker.number.int(100),
    planAmount: faker.number.int({ max: 10_000_000, multipleOf: 10_000 }),
    projectAddressName: generateFullAddress(),
    projectAddressDetail: faker.location.streetAddress(),
    nodeInfo: faker.helpers.arrayElements([
      '00',
      '01',
      '02',
      '03',
      '04',
      '05',
      '06',
      '07',
      '08',
      '09',
      '10',
      '11',
    ]),
    goodsList: Array.from(
      { length: faker.number.int({ min: 1, max: 5 }) },
      () => ({
        goodsName: faker.commerce.productName(),
        models: faker.helpers.arrayElement([
          '规格A',
          '规格B',
          '规格C',
          '规格D',
        ]),
        taxRate: faker.helpers.arrayElement([6, 9, 13]),
        num: faker.number.int({ min: 1, max: 100 }),
        unitPriceTax: faker.commerce.price({ min: 10, max: 1000 }),
        get amountTax() {
          return (this.num * this.unitPriceTax).toFixed(2);
        },
        remark: faker.lorem.sentence(5),
      }),
    ),
  };
}
const mockData = generateMockData();

export default eventHandler(async () => {
  return useResponseSuccess(mockData);
});
