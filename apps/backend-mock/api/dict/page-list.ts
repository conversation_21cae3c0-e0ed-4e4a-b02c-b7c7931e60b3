export default eventHandler(async (event) => {
  const { page, pageSize } = getQuery(event);
  const listData = [
    {
      pid: 'ZR010',
      code: 'ACCESS_STATE',
      name: '准入状态',
      dictName: '未提交',
      dictValue: 'ZR010',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR020',
      code: 'PROJECT_MODE',
      name: '项目模式',
      dictName: '采购业务',
      dictValue: '10',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR021',
      code: 'PROJECT_MODE',
      name: '项目模式',
      dictName: '销售业务',
      dictValue: '20',
      sort: 2,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR022',
      code: 'PROJECT_MODE',
      name: '项目模式',
      dictName: '一般贸易',
      dictValue: '30',
      sort: 3,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR030',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '待提交',
      dictValue: '00',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR031',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '审批中',
      dictValue: '10',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR032',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '已生效',
      dictValue: '20',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR033',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '已完成',
      dictValue: '30',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
  ];
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
