export default eventHandler(async () => {
  const listData = [
    {
      pid: '912',
      code: 'ELECTRONICS_SIGN_STATUS',
      name: '电签认证状态',
      dictName: '认证成功',
      dictValue: '1',
      sort: 1,
      isEdit: true,
      enable: true,
      appCode: 'middle_end_application',
      deleteFlag: 0,
    },
    {
      pid: '914',
      code: 'ELECTRONICS_SIGN_STATUS',
      name: '电签认证状态',
      dictName: '认证失败',
      dictValue: '3',
      sort: 1,
      isEdit: true,
      enable: true,
      appCode: 'middle_end_application',
      deleteFlag: 0,
    },
    {
      pid: '913',
      code: 'ELECTRONICS_SIGN_STATUS',
      name: '电签认证状态',
      dictName: '待认证',
      dictValue: '2',
      sort: 2,
      isEdit: true,
      enable: true,
      appCode: 'middle_end_application',
      deleteFlag: 0,
    },
  ];
  return useResponseSuccess(listData);
});
