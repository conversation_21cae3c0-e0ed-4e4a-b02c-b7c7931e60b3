import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

const routes: RouteRecordRaw[] = [
  {
    name: 'accountsMent',
    path: '/accountsMent',
    meta: {
      icon: 'carbon:workspace',
      title: $t('page.menu.accountsMent'),
    },
    children: [
      {
        name: 'accountsContract',
        path: '/accountsContract',
        component: () => import('#/views/accountsMent/accountsMent-contract/index.vue'),
        meta: {
          icon: '',
          title: $t('page.menu.accountsContract'),
        },
      },
      {
        name: 'accountsInvoice',
        path: '/accountsInvoice',
        component: () => import('#/views/accountsMent/accountsMent-invoice/index.vue'),
        meta: {
          icon: '',
          title: $t('page.menu.accountsInvoice'),
        },
      },
      {
        name: 'accounts',
        path: '/accounts',
        component: () => import('#/views/accountsMent/accountsMent-accounts/index.vue'),
        meta: {
          icon: '',
          title: $t('page.menu.accounts'),
        },
      },
      {
        name: 'accountsPool',
        path: '/accountsPool',
        component: () => import('#/views/accountsMent/accountsMent-pool/index.vue'),
        meta: {
          icon: '',
          title: $t('page.menu.accountsPool'),
        },
      },
    ],
  },
];

export default routes;
