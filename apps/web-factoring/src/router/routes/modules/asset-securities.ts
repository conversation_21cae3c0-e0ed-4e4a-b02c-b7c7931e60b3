import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: '',
      title: $t('page.asset-securities.title'),
    },
    name: 'AssetSecurities',
    path: '/asset-securities',
    children: [
      {
        name: 'AssetSecuritiesProject',
        path: '/asset-securities/project',
        component: () => import('#/views/asset-securities/project/index.vue'),
        meta: {
          icon: '',
          title: $t('page.asset-securities.project'),
        },
      },
    ],
  },
];

export default routes;
