import type { BaseEntity } from '#/api';

export interface accountsContract extends BaseEntity {
  // 上传日期
  uploadDate: string;
  // 合同编号
  contractCode: string;
  // 合同类型
  contractType: string;
  // 合同金额
  contractAmount: string;
  // 合同签署日期
  signedDate: string;
  // 使用状态
  useStatus: string;
  // 更新日期
  updateDate: string;
  // 操作状态
  status: string;
}

// export function getGroupPageListApi(params: PageListParams) {
//   return requestClient.get('/upms/group/page', { params });
// }
// export function addGroupApi(data: GroupInfo) {
//   return requestClient.post('/upms/group/add', data);
// }
// export function editGroupApi(data: GroupInfo) {
//   return requestClient.post('/upms/group/edit', data);
// }
// export function deleteGroupApi(id: string) {
//   return requestClient.post('/upms/group/delete', {}, { params: { id } });
// }
