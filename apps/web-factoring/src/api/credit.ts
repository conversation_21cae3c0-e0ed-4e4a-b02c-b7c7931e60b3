import type { BaseDataParams } from '@vben/types';

export interface CreditRecord {
  // 受理日期
  acceptDate: string;
  // 受理操作
  acceptAction: string;
  // 退回理由备注
  rejectReason: string;
}
export interface CreditContract {
  // 合同编号
  contractCode: string;
  // 合同名称
  contractName: string;
  // 签约状态
  signStatus: string;
  // 签署方式
  signMethod: string;
  // 签约方（甲方）
  partyA: string;
  // 签约方（乙方）
  partyB: string;
  // 发起签约时间
  signStartTime: string;
  // 签约完成时间
  signEndTime: string;
}

export interface CreditInfo extends BaseDataParams {
  // 授信企业
  companyName: string;
  // 统一社会信用代码
  scode: string;
  // 授信申请金额
  creditAmount?: string;
  // 综合授信编号
  creditCode?: string;
  // 授信申请期限
  creditTerm?: string;
  // 授信种类
  creditOpt?: string;
  // 授信合同
  creditContract: CreditContract[];
  // 授信受理记录
  creditRecord: CreditRecord[];
}

// export function getGroupPageListApi(params: PageListParams) {
//   return requestClient.get('/upms/group/page', { params });
// }
// export function addGroupApi(data: GroupInfo) {
//   return requestClient.post('/upms/group/add', data);
// }
// export function editGroupApi(data: GroupInfo) {
//   return requestClient.post('/upms/group/edit', data);
// }
// export function deleteGroupApi(id: string) {
//   return requestClient.post('/upms/group/delete', {}, { params: { id } });
// }
