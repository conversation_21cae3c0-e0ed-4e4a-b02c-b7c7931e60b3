/**
 * 授信记录
 */
export interface CreditLimitRecord {
  // 授信申请日期
  applyDate: string;
  // 授信申请金额
  applyAmount: string;
  // 授信申请期限
  applyTerm: string;
  // 授信决策时间
  decisionDate: string;
  // 授信决策金额
  decisionAmount: string;
  // 授信决策期限
  decisionTerm: string;
  // 授信种类
  creditType: string;
  // 授信方式
  creditMethod: string;
}
/**
 * 用信记录
 */
export interface UsageLimitRecord {
  // 用信日期
  usageLimitDate: string;
  // 出款编号
  paymentCode: string;
  // 融资编号
  financingCode: string;
  // 合同编号
  contractCode: string;
  // 用信额度（元）
  creditAmount: string;
}
/**
 * 结清记录
 */
export interface payoffInfo {
  // 结清日期
  payoffDate: string;
  // 还款编号
  repaymentCode: string;
  // 出款编号
  paymentCode: string;
  // 融资编号
  financingCode: string;
  // 合同编号
  contractCode: string;
  // 结清额度（元）
  payoffAmount: string;
}
/**
 * 我的额度
 */
export interface limitInfo {
  // 企业名称
  companyName: string;
  // 统一社会编码
  socialCreditCode: string;
  // 授信决策日期
  decisionDate: string;
  // 授信方式
  creditMethod: string;
  // 授信期限（月）
  creditTerm: string;
  // 授信有效期限
  creditExpiryDate: string;
  // 授信总额度
  totalCreditAmount: string;
  // 累计授信总额度
  accumulatedCreditAmount: string;
  // 当前用信额度
  currentUsageAmount: string;
  // 累计用额额度（元）
  accumulatedUsageAmount: string;
  // 已结清额度
  settledAmount: string;
  // 存量业务余额
  outstandingBalance: string;
  // 可用余额
  availableBalance: string;
  // 额度状态
  creditStatus: string;
  // 授信记录
  CreditRecordList: CreditLimitRecord[];
  // 用信记录
  UsageLimitRecordList: UsageLimitRecord[];
  // 结清记录
  payoffInfoList: payoffInfo[];
}

// export function getGroupPageListApi(params: PageListParams) {
//   return requestClient.get('/upms/group/page', { params });
// }
// export function addGroupApi(data: GroupInfo) {
//   return requestClient.post('/upms/group/add', data);
// }
// export function editGroupApi(data: GroupInfo) {
//   return requestClient.post('/upms/group/edit', data);
// }
// export function deleteGroupApi(id: string) {
//   return requestClient.post('/upms/group/delete', {}, { params: { id } });
// }
