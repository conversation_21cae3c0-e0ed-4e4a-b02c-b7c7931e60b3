import type { CreditContract } from '#/api';

export interface signInfo {
  // 签署方
  signParty: string;
  // 签置企业
  signEnterprise: string;
  // 签署人
  signer: string;
  // 签署链接
  signLink: string;
  // 签署进度
  signProgress: string;
  // 签署日期
  signDate: string;
}

export interface ContractInfo extends CreditContract {
  // 合同编号
  contractCode: string;
  // 合同名称
  contractName: string;
  // 签约状态
  signStatus: string;
  // 签署方式
  signMethod: string;
  // 签约方（甲方）
  partyA: string;
  // 签约方（乙方）
  partyB: string;
  // 发起签约时间
  signStartTime: string;
  // 签约完成时间
  signEndTime: string;
  // 签署信息
  signInfoList: signInfo[];
}

// export function getGroupPageListApi(params: PageListParams) {
//   return requestClient.get('/upms/group/page', { params });
// }
// export function addGroupApi(data: GroupInfo) {
//   return requestClient.post('/upms/group/add', data);
// }
// export function editGroupApi(data: GroupInfo) {
//   return requestClient.post('/upms/group/edit', data);
// }
// export function deleteGroupApi(id: string) {
//   return requestClient.post('/upms/group/delete', {}, { params: { id } });
// }
