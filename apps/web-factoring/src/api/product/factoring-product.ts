import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface FactoringProduct extends BaseDataParams {
  // 产品名称
  productName: string;
  // 产品编码
  productCode: string;
  // 产品类型
  productType: string;
  // 操作状态
  status: string;
  // 上架状态
  upStatus: string;
  // 最后上架日期
  lastUpDate?: Date;
  // 还款方：1=融资客户，2=债务人
  repaymentType: string;
  // 保理类型；1=保理融资，2=再保理融资
  factoringType: string;
  // 支持操作模式：1=明保理，2=暗保理
  factoringMode: string;
  // 支持保理方向：1=正向保理，2=反向保理
  factoringDirection: string;
  // 是否脱核：0=否，1=是
  factoringAutoRights: string;
  // 追索权要求：0=否，1=是
  rightOfRecourse: string;
  // 签署协议配置：1=必须签署(生效前签署)，2=可选签署(生效后签署)，3=跳过签署
  signConfig: string;
  // 产品介绍
  productDesc?: string;
  // 还款方式：
  repaymentMethod: string;
  // 还款期数最小值
  repaymentPeriodsMin?: number;
  // 还款期数最大值
  repaymentPeriodsMax?: number;
  // 年化利率最小值
  annualizedInterestRateMin: number;
  // 年化利率最大值
  annualizedInterestRateMax: number;
  // 融资比例最小值
  financingRatioMin: number;
  // 融资比例最大值
  financingRatioMax: number;
  // 手续费最小值
  serviceFeeMin?: number;
  // 手续费最大值
  serviceFeeMax?: number;
  // 固定手续费最小值
  serviceFeeFixedMin?: number;
  // 固定手续费最大值
  serviceFeeFixedMax?: number;
  // 是否有宽限期：0=无，1=有
  isGracePeriod: string;
  // 宽限期类型：1=融资宽限期，2=应收账款宽限期
  gracePeriodType?: string;
  // 融资/应收账款宽限期（天）最小值
  gracePeriodMin: number;
  // 融资/应收账款宽限期（天）最大值
  gracePeriodMax: number;
  // 是否加息：0=无，1=是
  isInterestAdd: string;
  // 加息天数（天）最小值
  interestAddDaysMin?: number;
  // 加息天数（天）最大值
  interestAddDaysMax?: number;
  // 加息方式：1=固定加息，2=浮动加息
  interestAddMethod?: string;
  // 固定/浮动加息率最小值
  interestAddRateMin: number;
  // 固定/浮动加息率最大值
  interestAddRateMax: number;
  // 罚息类型：1=阶梯值，2=固定值
  penaltyInterestType: string;
  // 年化罚息固定利率
  penaltyInterestAnnualRate: number;
  // 风险系数最小值
  riskCoefficientMin?: number;
  // 风险系数最大值
  riskCoefficientMax?: number;
}

export async function getProductPageListApi(params: PageListParams) {
  return requestClient.get<FactoringProduct[]>('/upms/user/page', { params });
}
export async function addProductApi(data: FactoringProduct) {
  return requestClient.post<FactoringProduct>('/upms/user/add', data);
}
export async function editProductApi(data: FactoringProduct) {
  return requestClient.post<FactoringProduct>('/upms/user/edit', data);
}
export async function infoProductApi(data: FactoringProduct) {
  return requestClient.post<FactoringProduct>('/upms/user/info', data);
}
export async function delProductApi(id: string) {
  return requestClient.post('/upms/user/delete', {}, { params: { id } });
}
