<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ContractInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';

import { Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

// import Form from './components/contractMentForm.vue';

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同名称',
      },
      fieldName: 'category',
      label: '合同名称',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      componentProps: {
        placeholder: '请输入合同编码',
      },
      label: '合同编码',
    },
    {
      component: 'DatePicker',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择发起签署日期',
      },
      fieldName: 'vo',
      label: '发起签署日期',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择完成签署日期',
      },
      fieldName: 'vo',
      label: '完成签署日期',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      componentProps: {
        placeholder: '请输入签约方',
      },
      label: '签约方',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择签约方式',
      },
      fieldName: 'kehu',
      label: '签约方式',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择签署状态',
      },
      fieldName: 'caozuo',
      label: '签署状态',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
  // 按下回车时是否提交表单
  submitOnEnter: true,
};

const gridOptions: VxeGridProps<ContractInfo> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  height: 'auto',

  data: [
    {
      contractCode: 'HT20230001',
      contractName: '软件开发服务协议',
      partyA: 'XX科技有限公司',
      partyB: 'YY软件有限公司',
      signEndTime: '2023-05-15 14:15:00',
      signMethod: '电子签约',
      signStartTime: '2023-05-10 09:30:00',
      signStatus: '已完成',
      signInfoList: [],
    },
    {
      contractCode: 'HT20230002',
      contractName: '产品采购合同',
      signStatus: '进行中',
      signMethod: '纸质签约',
      partyA: 'AA制造有限公司',
      partyB: 'BB贸易有限公司',
      signStartTime: '2023-06-01 10:00:00',
      signEndTime: '',
      signInfoList: [],
    },
    {
      contractCode: 'HT20230003',
      contractName: '战略合作协议',
      signStatus: '未开始',
      signMethod: '电子签约',
      partyA: 'CC集团公司',
      partyB: 'DD实业有限公司',
      signStartTime: '',
      signEndTime: '',
      signInfoList: [],
    },
  ],
  columns: [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'contractCode', title: '合同编号' },
    { field: 'contractName', title: '合同名称' },
    { field: 'partyA', title: '签约方' },
    { field: 'signMethod', title: '签署方式' },
    { field: 'signStatus', formatter: 'formatDateTime', title: '签署状态' },
    { field: 'signStartTime', title: '发起签署日期' },
    { field: 'signEndTime', title: '完成签署日期' },
    { slots: { default: 'action' }, title: '操作' },
  ],
  keepSource: true,
  pagerConfig: {},
  //   proxyConfig: {
  //     ajax: {
  //       query: async ({ page }, formValues) => {
  //         message.success(`Query params: ${JSON.stringify(formValues)}`);
  //         return await getExampleTableApi({
  //           page: page.currentPage,
  //           pageSize: page.pageSize,
  //           ...formValues,
  //         });
  //       },
  //     },
  //   },
  toolbarConfig: {
    // 是否显示搜索表单控制按钮
    // @ts-ignore 正式环境时有完整的类型声明
    // search: true,
  },
};
const editSuccess = () => {};
const handleDetail = (row: ContractInfo) => {
  // modalApi.setData(row).open();
  openFormPopup(true, { signMethod: row.signMethod });
};
// const editSuccess = () => {};

const goSign = () => {};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [Grid] = useVbenVxeGrid({ formOptions, gridOptions });
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="handleDetail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink @click="goSign()">
            {{ $t('base.goSign') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
  </Page>
</template>
