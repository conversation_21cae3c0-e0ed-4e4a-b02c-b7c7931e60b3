<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { EnterpriseInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Button, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delEnterpriseApi, getEnterprisePageListApi } from '#/api';

import Form from './form.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Input',
      fieldName: 'creditCode',
      label: '统一社会信用代码',
    },
    {
      component: 'Select',
      fieldName: 'productType',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务角色',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'establishDate',
      label: '成立日期',
    },
  ],
  fieldMappingTime: [['establishDate', ['establishDateStart', 'establishDateEnd'], 'x']],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'companyName', title: '企业名称' },
    { field: 'creditCode', title: '统一社会信用代码' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: '',
        },
      },
    },
    {
      field: 'enableStatus',
      title: '启用状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: '',
        },
      },
    },
    { field: 'establishDate', title: '成立日期', formatter: 'formatDate' },
    { field: 'legalRepresentative', title: '法定代表人' },
    { field: 'industry', title: '所属行业' },
    { field: 'registrationStatus', title: '登记状态' },
    { field: 'createName', title: '创建人' },
    { field: 'createTime', title: '创建日期', formatter: 'formatDate' },
  ],
  height: 'auto',
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async ({ page }, formValues) => {
        return await getEnterprisePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: EnterpriseInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const del = async (row: EnterpriseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delEnterpriseApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="user" @click="edit(row)"> 变更 </MenuItem>
                <MenuItem key="view"> 启用 </MenuItem>
                <MenuItem key="view"> 禁用 </MenuItem>
                <MenuItem key="view"> 作废 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
