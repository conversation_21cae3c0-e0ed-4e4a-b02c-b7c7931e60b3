<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { EnterpriseInfo } from '#/api';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import {
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  RangePicker,
  Row,
  Select,
  Textarea,
} from 'ant-design-vue';

import { addEnterpriseApi, editEnterpriseApi, infoEnterpriseApi } from '#/api';
import AccountInfo from '#/views/access-manage/enterprise/components/account-info.vue';
import ContactInfo from '#/views/access-manage/enterprise/components/contact-info.vue';
import InvoiceInfo from '#/views/access-manage/enterprise/components/invoice-info.vue';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();
// 根据接口定义初始化信息
const defaultForm: Partial<EnterpriseInfo> = {
  companyName: '',
  creditCode: '',
  industry: '',
  legalRepresentative: '',
  organCode: '',
  taxpayerNumber: '',
  busRegistrationNumber: '',
  registrationStatus: '',
  establishDate: undefined,
  taxpayerQualification: '',
  businessTermStart: undefined,
  businessTermEnd: undefined,
  principalApprovalTime: undefined,
  isIpo: null,
  companyType: '',
  personnelPhone: '',
  personnelScale: '',
  registeredCapital: 0,
  companyContactEmail: '',
  companyOfficialWebsite: '',
  actualCapital: 0,
  registerOrgan: '',
  financialAccountingNumber: '',
  auxiliaryAccountingCode: '',
  registeredAddress: {},
  registeredAddressAll: '',
  businessAddress: {},
  businessAddressAll: '',
  businessScope: '',
  remarks: '',
  businessRoles: '',
  status: '',
  enableStatus: '',
  invoiceList: [],
  accountList: [],
  contactList: [],
};

const enterpriseInfo = ref<Partial<EnterpriseInfo>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
// 修改4：更新验证规则以匹配Enterprise接口字段
const rules: Record<string, Rule[]> = {
  companyName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  creditCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
  industry: [{ required: false, message: '请选择所属行业', trigger: 'change' }],
  legalRepresentative: [{ required: false, message: '请输入法定代表人', trigger: 'blur' }],
  organCode: [{ required: false, message: '请输入组织机构代码', trigger: 'blur' }],
  taxpayerNumber: [{ required: false, message: '请输入纳税人识别号', trigger: 'blur' }],
  busRegistrationNumber: [{ required: false, message: '请输入工商注册号', trigger: 'blur' }],
  registrationStatus: [{ required: false, message: '请输入登记状态', trigger: 'blur' }],
  establishDate: [{ required: false, message: '请选择成立日期', trigger: 'change' }],
  taxpayerQualification: [{ required: false, message: '请输入纳税人资质', trigger: 'blur' }],
  businessTermStart: [{ required: false, message: '请选择营业期限开始', trigger: 'change' }],
  businessTermEnd: [{ required: false, message: '请选择营业期限结束', trigger: 'change' }],
  principalApprovalTime: [{ required: false, message: '请选择核准日期', trigger: 'change' }],
  companyType: [{ required: false, message: '请输入企业类型', trigger: 'blur' }],
  personnelPhone: [{ required: false, message: '请输入企业联系电话', trigger: 'blur' }],
  personnelScale: [{ required: false, message: '请输入人员规模', trigger: 'blur' }],
  registeredCapital: [{ required: false, message: '请输入注册资本', trigger: 'blur' }],
  companyContactEmail: [{ required: false, message: '请输入企业联系邮箱', trigger: 'blur' }],
  companyOfficialWebsite: [{ required: false, message: '请输入企业官网', trigger: 'blur' }],
  actualCapital: [{ required: false, message: '请输入实缴资本', trigger: 'blur' }],
  registerOrgan: [{ required: false, message: '请输入登记机关', trigger: 'blur' }],
  financialAccountingNumber: [{ required: false, message: '请输入财务核算账簿编号', trigger: 'blur' }],
  auxiliaryAccountingCode: [{ required: false, message: '请输入辅助核算编码', trigger: 'blur' }],
  businessScope: [{ required: false, message: '请输入经营范围', trigger: 'blur' }],
  remarks: [{ required: false, message: '请输入备注', trigger: 'blur' }],
};
const title = computed(() => {
  return enterpriseInfo.value.id ? '编辑企业' : '新增企业';
});
const invoiceGridRef = ref();
const accountGridRef = ref();
const contactGridRef = ref();

const init = async (data: EnterpriseInfo) => {
  if (data.id) {
    enterpriseInfo.value = await infoEnterpriseApi({ id: data.id as string });
    invoiceGridRef.value.setInvoiceData(enterpriseInfo.value.invoiceList);
    accountGridRef.value.setAccountData(enterpriseInfo.value.accountList);
    contactGridRef.value.setContactData(enterpriseInfo.value.contactList);
  }
};

const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  enterpriseInfo.value.invoiceList = invoiceGridRef.value?.getInvoiceData() || [];
  enterpriseInfo.value.accountList = accountGridRef.value?.getAccountData() || [];
  enterpriseInfo.value.contactList = contactGridRef.value?.getContactData() || [];
  changeOkLoading(true);
  let api = addEnterpriseApi;
  if (enterpriseInfo.value.id) {
    api = editEnterpriseApi;
  }
  try {
    const res = await api(enterpriseInfo.value as EnterpriseInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="enterpriseInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基础信息 -->
      <BasicCaption content="基础信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="企业名称" name="companyName">
            <Input v-model:value="enterpriseInfo.companyName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="统一社会信用代码" name="creditCode">
            <Input v-model:value="enterpriseInfo.creditCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="所属行业" name="industry">
            <Select v-model:value="enterpriseInfo.industry" :options="getDictList('industry')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="法定代表" name="legalRepresentative">
            <Input v-model:value="enterpriseInfo.legalRepresentative" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="组织机构代码" name="organCode">
            <Input v-model:value="enterpriseInfo.organCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="纳税人识别号" name="taxpayerNumber">
            <Input v-model:value="enterpriseInfo.taxpayerNumber" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="工商注册号" name="busRegistrationNumber">
            <Input v-model:value="enterpriseInfo.busRegistrationNumber" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="登记状态" name="registrationStatus">
            <Select v-model:value="enterpriseInfo.registrationStatus" :options="getDictList('registrationStatus')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="成立日期" name="establishDate">
            <DatePicker v-model:value="enterpriseInfo.establishDate" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="纳税人资质" name="taxpayerQualification">
            <Input v-model:value="enterpriseInfo.taxpayerQualification" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="营业期限" name="businessTerm">
            <RangePicker v-model:value="enterpriseInfo.businessTerm" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="核准日期" name="principalApprovalTime">
            <DatePicker v-model:value="enterpriseInfo.principalApprovalTime" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否上市公司" name="isIpo">
            <Select v-model:value="enterpriseInfo.isIpo" :options="getDictList('isIpo')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="企业类型" name="companyType">
            <Select v-model:value="enterpriseInfo.companyType" :options="getDictList('companyType')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="企业联系电话" name="personnelPhone">
            <Input v-model:value="enterpriseInfo.personnelPhone" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="人员规模" name="personnelScale">
            <Input v-model:value="enterpriseInfo.personnelScale" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="注册资本（万元）" name="registeredCapital">
            <InputNumber v-model:value="enterpriseInfo.registeredCapital" :controls="false" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="企业联系邮箱" name="companyContactEmail">
            <Input v-model:value="enterpriseInfo.companyContactEmail" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="企业官网" name="companyOfficialWebsite">
            <Input v-model:value="enterpriseInfo.companyOfficialWebsite" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="实缴资本（万元）" name="actualCapital">
            <InputNumber v-model:value="enterpriseInfo.actualCapital" :controls="false" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="登记机关" name="registerOrgan">
            <Input v-model:value="enterpriseInfo.registerOrgan" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="财务核算账簿编号" name="financialAccountingNumber">
            <Input v-model:value="enterpriseInfo.financialAccountingNumber" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="辅助核算编码" name="auxiliaryAccountingCode">
            <Input v-model:value="enterpriseInfo.auxiliaryAccountingCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="注册地址" name="registeredAddressAll">
            <Input v-model:value="enterpriseInfo.registeredAddressAll" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="经营地址" name="businessAddressAll">
            <Input v-model:value="enterpriseInfo.businessAddressAll" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="经营范围" name="businessScope">
            <Textarea v-model:value="enterpriseInfo.businessScope" :rows="4" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="enterpriseInfo.remarks" :rows="4" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="开票信息" />
      <InvoiceInfo ref="invoiceGridRef" />

      <BasicCaption content="账户信息" />
      <AccountInfo ref="accountGridRef" />

      <BasicCaption content="联系人信息" />
      <ContactInfo ref="contactGridRef" />
    </Form>
  </BasicPopup>
</template>
