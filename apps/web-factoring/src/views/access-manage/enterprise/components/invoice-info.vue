<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Button, Input, message, Switch } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

const invoiceList = ref<InvoiceInfo[]>([]);

// 新增初始化方法
const setInvoiceData = (data: InvoiceInfo[]) => {
  invoiceList.value = data;
  if (gridApi.grid) {
    gridApi.grid.reloadData(invoiceList.value);
  }
};

// 表格配置
const gridOptions: VxeTableGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  editRules: {
    titleName: [{ required: true, message: '请输入抬头名称' }],
    taxpayerNumber: [{ required: true, message: '请输入纳税人识别号' }],
    depositBank: [{ required: true, message: '请输入开户行' }],
    bankAccount: [{ required: true, message: '请输入银行账号' }],
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'status',
      title: '状态',
      slots: { default: 'edit_status' },
      minWidth: '100px',
      fixed: 'left',
    },
    { field: 'titleName', title: '抬头名称', editRender: {}, slots: { edit: 'edit_title_name' }, minWidth: '160px' },
    {
      field: 'taxpayerNumber',
      title: '纳税人识别号',
      editRender: {},
      slots: { edit: 'edit_taxpayer_number' },
      minWidth: '160px',
    },
    { field: 'depositBank', title: '开户行', editRender: {}, slots: { edit: 'edit_deposit_bank' }, minWidth: '160px' },
    {
      field: 'bankAccount',
      title: '银行账号',
      editRender: {},
      slots: { edit: 'edit_bank_account' },
      minWidth: '160px',
    },
    {
      field: 'depositAddress',
      title: '开户地址',
      editRender: {},
      slots: { edit: 'edit_deposit_address' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '是否默认发票信息',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
  ],
  data: invoiceList.value,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 增加发票行
const addInvoice = async () => {
  const record = {};
  const $grid = gridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};

// 删除发票行
const removeInvoice = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success($t('base.resSuccess'));
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  getInvoiceData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const { visibleData } = gridApi.grid.getTableData();
      return visibleData;
    } else {
      return [];
    }
  },
  setInvoiceData,
});
</script>

<template>
  <div>
    <Grid>
      <template #toolbarTools>
        <Button class="mr-2" type="primary" @click="addInvoice">增行</Button>
        <Button class="mr-2" danger @click="removeInvoice">删行</Button>
      </template>
      <template #edit_status="{ row }">
        <Switch v-model:checked="row.status" :checked-value="1" :un-checked-value="0" />
      </template>
      <template #edit_title_name="{ row }">
        <Input v-model:value="row.titleName" placeholder="请输入抬头名称" />
      </template>
      <template #edit_taxpayer_number="{ row }">
        <Input v-model:value="row.taxpayerNumber" placeholder="请输入纳税人识别号" />
      </template>
      <template #edit_deposit_bank="{ row }">
        <Input v-model:value="row.depositBank" placeholder="请输入开户行" />
      </template>
      <template #edit_bank_account="{ row }">
        <Input v-model:value="row.bankAccount" placeholder="请输入银行账号" />
      </template>
      <template #edit_deposit_address="{ row }">
        <Input v-model:value="row.depositAddress" placeholder="请输入开户地址" />
      </template>
      <template #edit_is_default="{ row }">
        <Switch v-model:checked="row.isDefault" :checked-value="1" :un-checked-value="0" />
      </template>
    </Grid>
  </div>
</template>

<style scoped></style>
