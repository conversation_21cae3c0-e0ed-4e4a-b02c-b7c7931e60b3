<script setup lang="ts">
import { nextTick, reactive, ref } from 'vue';

import { PlusOutlined } from '@ant-design/icons-vue';
import { cloneDeep } from 'lodash-es';

const formModel = ref({
  specValueList: [],
});

const rules = {
  specName: [{ required: true, message: '请输入规格名' }],
};

const formModelRef = ref();

const init = (data) => {
  formModelRef.value.clearValidate();
  const formData = cloneDeep(data);
  formData.specValueList = formData.specValueJson ? JSON.parse(formData.specValueJson) : [];
  formModel.value = formData;
};
const state = reactive({
  inputVisible: false,
  inputValue: '',
});
const inputRef = ref();

const handleClose = (removedTag: string) => {
  formModel.value.specValueList = formModel.value.specValueList.filter((tag) => tag !== removedTag);
};

const showInput = () => {
  state.inputVisible = true;
  nextTick(() => {
    inputRef.value.focus();
  });
};

const handleInputConfirm = () => {
  const inputValue = state.inputValue;
  let tags = formModel.value.specValueList;
  if (inputValue && !tags.includes(inputValue)) {
    tags = [...tags, inputValue];
  }
  formModel.value.specValueList = tags;
  state.inputValue = '';
  state.inputVisible = false;
};

const submit = () => {
  formModelRef.value.validate();
  const formData = cloneDeep(formModel.value);
  formData.specValueJson = JSON.stringify(formData.specValueList);
  return formData;
};

defineExpose({ init, submit });
</script>

<template>
  <a-form ref="formModelRef" :model="formModel" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
    <a-form-item label="规格名" name="specName">
      <a-input v-model:value="formModel.specName" />
    </a-form-item>
    <a-form-item label="规格值">
      <a-tag
        v-for="(tag, index) in formModel.specValueList"
        :key="tag + index"
        closable
        color="blue"
        @close="handleClose(tag)"
      >
        {{ tag }}
      </a-tag>
      <a-input
        v-if="state.inputVisible"
        ref="inputRef"
        v-model:value="state.inputValue"
        type="text"
        size="small"
        :style="{ width: '78px' }"
        @blur="handleInputConfirm"
        @keyup.enter="handleInputConfirm"
      />
      <a-tag v-else class="cursor-pointer" @click="showInput">
        <PlusOutlined />
        添加
      </a-tag>
    </a-form-item>
  </a-form>
</template>

<style></style>
