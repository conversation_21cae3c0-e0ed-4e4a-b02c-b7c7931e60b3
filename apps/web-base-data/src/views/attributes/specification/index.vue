<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Modal as AModal, message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addSpecificationApi, deleteSpecificationApi, editSpecificationApi, getSpecificationPageListApi } from '#/api';
import SpecificationForm from '#/views/attributes/specification/components/specification-form.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'specName',
      label: '规格名',
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'specName', title: '规格名' },
    {
      field: 'specValueJson',
      title: '规格值',
      formatter({ cellValue }) {
        if (cellValue) {
          const list = JSON.parse(cellValue);
          return list.join('，');
        } else {
          return '';
        }
      },
      slots: { default: 'specValueList' },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { manufacturerCode: string; manufacturerName: string; status: string },
      ) => {
        const pageList = await getSpecificationPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
        pageList.records?.forEach((item) => {
          item.specValueList = item.specValueJson ? JSON.parse(item.specValueJson) : [];
        });
        return pageList;
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const specificationFormRef = ref();
const modalTitle = ref('新增规格');
const specificationForm = ref({});

const [Modal, modalApi] = useVbenModal({
  onOpened: async () => {
    specificationFormRef.value.init(specificationForm.value);
  },
  onConfirm: async () => {
    const formData = await specificationFormRef.value.submit();
    let api = addSpecificationApi;
    if (formData.pid) {
      api = editSpecificationApi;
    }
    await api(formData);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
});

const addSpecification = () => {
  modalTitle.value = '新增规格';
  specificationForm.value = {};
  modalApi.open();
};

const editSpecification = (row) => {
  modalTitle.value = '编辑规格';
  specificationForm.value = row;
  modalApi.open();
};

const delRow = (row) => {
  const pidList = [row.pid];
  AModal.confirm({
    title: '确认删除',
    content: '确认删除此规格？',
    onOk: async () => {
      await deleteSpecificationApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="addSpecification">新增</a-button>
      </template>
      <template #specValueList="{ row }">
        <Tag v-for="item in row.specValueList" :key="item" color="blue">{{ item }}</Tag>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="editSpecification(row)">编辑</a-typography-link>
          <a-typography-link type="danger" @click="delRow(row)">删除</a-typography-link>
        </a-space>
      </template>
    </Grid>

    <Modal :title="modalTitle">
      <SpecificationForm ref="specificationFormRef" />
    </Modal>
  </Page>
</template>

<style></style>
