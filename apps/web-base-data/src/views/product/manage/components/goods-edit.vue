<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SpuInfo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep } from '@vben/utils';

import { PlusOutlined } from '@ant-design/icons-vue';
import {
  AutoComplete,
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  message,
  Modal,
  Row,
  Select,
  Space,
  TypographyLink,
} from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addSpuApi,
  editSpuApi,
  getAttributeUnitList,
  getSpecificationListApi,
  getSpuInfoApi,
  getTaxonomyListApi,
} from '#/api';
import { deleteSkuApi, disableSku<PERSON>pi, editSkuApi, enableSkuApi } from '#/api/product/sku';

const colSpan = { md: 12, sm: 24 };
const init = (data: Partial<SpuInfo> = {}) => {
  if (data.pid) {
    getDetail(data.pid);
  }
};
const getDetail = async (pid) => {
  const res = await getSpuInfoApi({ pid });
  goodsForm.value = defaultsDeep(res, {
    spuSpecList: [],
    newSkuList: [],
    skuList: [],
  });
  const columns = cloneDeep(newSkuColumns);
  const spuSpecList: SpuInfo.spuSpecList[] = [];
  goodsForm.value.skuList?.forEach((specItem) => {
    if (specItem.specJson) {
      const specJsonList = JSON.parse(specItem.specJson);
      specJsonList.forEach((nItem) => {
        specItem[nItem.specName] = nItem.specValue;
        // specNameList.push(nItem.specName);
        if (!spuSpecList.find((item) => item.specName === nItem.specName)) {
          spuSpecList.push({
            specName: nItem.specName,
          });
        }
      });
    }
  });
  console.log(spuSpecList, 'spuSpecList');
  spuSpecList.forEach((specItem) => {
    columns[1].children.push({
      field: specItem.specName,
      title: specItem.specName,
      minWidth: '160px',
    });
  });
  await skuGridApi.grid.loadColumn(columns);
  await skuGridApi.grid.reloadData(goodsForm.value.skuList);
};
const save = async () => {
  await formRef.value.validate();
  const { visibleData } = newSkuGridApi.grid.getTableData();
  changeOkLoading(true);
  let api = addSpuApi;
  if (goodsForm.value.pid) {
    api = editSpuApi;
  }
  const formData = cloneDeep(goodsForm.value);
  formData.skuList = formData.newSkuList;
  delete formData.spuSpecList;
  delete formData.newSkuList;
  try {
    console.log(visibleData);
    console.log(formData);
    await api(formData);
    message.success('保存成功');
    // closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const formRef = ref();
const goodsForm = ref<SpuInfo>({
  spuSpecList: [],
  newSkuList: [],
  skuList: [],
  spuName: '',
  spuCode: '',
  categoryId: '',
  specificationModel: '',
  measureUnit: '',
});
const rules = {
  spuCode: [{ required: true, message: '请输入商品编码', trigger: 'change' }],
  spuName: [{ required: true, message: '请输入商品名称', trigger: 'change' }],
  categoryId: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
};
const addSku = () => {
  goodsForm.value.spuSpecList.push({ skuName: '' });
};
const delSku = (index) => {
  goodsForm.value.spuSpecList.splice(index, 1);
};
const specList = ref([]);
const getSpecList = async () => {
  const res = await getSpecificationListApi();
  specList.value = res.map((item) => {
    return { label: item.specName, value: item.specName, specValueJson: item.specValueJson };
  });
};
getSpecList();
const selectSpec = (value, option, skuItem) => {
  skuItem.specValueList = option.specValueJson ? JSON.parse(option.specValueJson) : [];
};
const state = reactive({
  inputVisible: false,
  inputValue: '',
});
const inputRef = ref();
const handleDeleteSpecValue = (tag, list) => {
  list.splice(list.indexOf(tag), 1);
};
const showInput = () => {
  state.inputVisible = true;
};

const handleInputConfirm = (skuItem) => {
  const inputValue = state.inputValue;
  let tags = skuItem.specValueList ?? [];
  if (inputValue && !tags.includes(inputValue)) {
    tags = [...tags, inputValue];
  }
  skuItem.specValueList = tags;
  state.inputValue = '';
  state.inputVisible = false;
};
const newSkuColumns = [
  { type: 'checkbox', width: '60px', fixed: 'left' },
  {
    field: 'spuSpecList',
    title: '商品规格',
    minWidth: '100px',
    children: [],
  },
  { field: 'skuName', title: 'SKU名称', editRender: {}, slots: { edit: 'edit_sku_name' }, minWidth: '160px' },
  { field: 'skuCode', title: 'SKU编码', editRender: {}, slots: { edit: 'edit_sku_code' }, minWidth: '160px' },
  {
    field: 'status',
    title: '状态',
    minWidth: '160px',
    formatter: ['formatBoolean', { true: '启用', false: '禁用' }],
  },
  {
    field: 'action',
    title: '操作',
    minWidth: '160px',
    fixed: 'right',
    slots: { default: 'action' },
  },
];
const newSkuGridOptions: VxeTableGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  editRules: {
    bankName: [{ required: true, message: '请输入账户名称' }],
  },
  columns: newSkuColumns,
  data: goodsForm.value.newSkuList,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    // slots: {
    //   tools: 'toolbarTools',
    // },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [NewSkuGrid, newSkuGridApi] = useVbenVxeGrid({
  gridOptions: newSkuGridOptions,
});
/**
 * 处理列表数据 - 生成笛卡尔积
 */
const cartesianProduct = (specList) => {
  const result = [];
  function helper(arr, index) {
    if (index === specList.length) {
      result.push(arr);
      return;
    }
    const currentSpec = specList[index];
    currentSpec.specValueList.forEach((value) => {
      helper(
        arr.concat({
          specName: currentSpec.skuName,
          specValue: value,
        }),
        index + 1,
      );
    });
  }
  helper([], 0);
  return result;
};

/**
 * 计算生成的编号是否重复
 */
const calculateCode = (codeNum, codeName, list, boolStr, callback) => {
  if (boolStr) {
    callback(codeNum);
  } else {
    let newCodeName = '';
    if (codeNum < 10) {
      newCodeName = `${codeName}00${codeNum}`;
    } else if (codeNum < 100) {
      newCodeName = `${codeName}0${codeNum}`;
    } else {
      newCodeName = `${codeName}${codeNum}`;
    }
    if (list.includes(newCodeName)) {
      codeNum++;
      calculateCode(codeNum, codeName, list, false, callback);
    } else {
      calculateCode(codeNum, codeName, list, true, callback);
    }
  }
};

/**
 * 生成SKU列表
 */
const createSku = () => {
  if (!goodsForm.value.spuName) {
    message.error('请填写商品名称');
    return;
  }

  // 保存当前的规格配置
  const currentSpecList = cloneDeep(goodsForm.value.spuSpecList || []);

  // 重置SKU列表
  goodsForm.value.newSkuList = [];

  // 获取有效的规格列表（有规格值的）
  const validSpecList = currentSpecList.filter(
    (item) => item.skuName && item.specValueList && item.specValueList.length > 0,
  );

  if (validSpecList.length === 0) {
    // 没有规格时，创建一个默认SKU
    goodsForm.value.newSkuList.push({
      skuSpecList: [],
      skuName: goodsForm.value.spuName,
      status: 1,
      indexNum: 0,
    });
  } else {
    // 生成笛卡尔积
    const skuSpecList = cartesianProduct(validSpecList);

    // 为每个SKU组合创建SKU项
    skuSpecList.forEach((item, index) => {
      goodsForm.value.newSkuList.push({
        skuSpecList: item,
        status: 1,
        indexNum: index,
      });
    });
  }

  // 获取已有编码（这里假设没有已存在的SKU，实际项目中可能需要从API获取）
  const hasSkuCodeList = [];

  // 设置SKU名称和规格值
  goodsForm.value.newSkuList.forEach((item) => {
    const newSkuName = [];
    item.skuSpecList?.forEach((cItem) => {
      item[cItem.specName] = cItem.specValue;
      newSkuName.push(cItem.specValue);
    });

    // 默认名称：多个规格中间加空格
    if (!item.skuName) {
      item.skuName = newSkuName.length > 0 ? newSkuName.join(' ') : goodsForm.value.spuName;
    }
  });

  // 生成SKU编码
  let newSkuCode = 0;
  for (let i = 0; i < goodsForm.value.newSkuList.length; i++) {
    newSkuCode++;
    const spuCode = goodsForm.value.spuCode || '';
    calculateCode(newSkuCode, spuCode, hasSkuCodeList, false, (result) => {
      const item = goodsForm.value.newSkuList[i];
      if (!item.skuCode) {
        newSkuCode = result;
        if (newSkuCode < 10) {
          item.skuCode = `${spuCode}00${newSkuCode}`;
        } else if (newSkuCode < 100) {
          item.skuCode = `${spuCode}0${newSkuCode}`;
        } else {
          item.skuCode = `${spuCode}${newSkuCode}`;
        }
      }
    });
  }

  message.success(`成功生成 ${goodsForm.value.newSkuList.length} 个SKU`);
  const columns = cloneDeep(newSkuColumns);
  goodsForm.value.spuSpecList.forEach((specItem) => {
    columns[1].children.push({
      field: specItem.skuName,
      title: specItem.skuName,
      minWidth: '160px',
    });
  });
  newSkuGridApi.grid.loadColumn(columns);
  newSkuGridApi.grid.reloadData(goodsForm.value.newSkuList);
  console.log(goodsForm.value);
};
const batchDelNewSku = () => {
  const res = newSkuGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  goodsForm.value.newSkuList = goodsForm.value.newSkuList.filter((item) => !res.includes(item));
  newSkuGridApi.grid.reloadData(goodsForm.value.newSkuList);
};
const batchChangeStatusNewSku = (status) => {
  const res = newSkuGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  res.forEach((item) => {
    item.status = status;
  });
  newSkuGridApi.grid.reloadData(goodsForm.value.newSkuList);
};
const [SkuGrid, skuGridApi] = useVbenVxeGrid({
  gridOptions: newSkuGridOptions,
  gridEvents: {
    editClosed: ({ row }) => {
      skuEdit(row);
    },
  },
});
const skuEdit = async (row) => {
  await editSkuApi(row);
  message.success('保存成功');
};
const changeSkuStatus = (row, status: 0 | 1) => {
  const pidList = [row.pid];
  const operation = {
    1: {
      label: '启用',
      api: enableSkuApi,
    },
    0: {
      label: '禁用',
      api: disableSkuApi,
    },
  };
  Modal.confirm({
    title: `确认${operation[status].label}`,
    content: `确认${operation[status].label}此SKU吗？`,
    async onOk() {
      await operation[status].api(pidList);
      message.success('操作成功');
      row.status = status;
      // await skuGridApi.reload();
    },
  });
};
const batchDelSku = async () => {
  const res = skuGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList: string[] = [];
  res.forEach((item) => {
    pidList.push(item.pid);
  });
  await deleteSkuApi(pidList);
  message.success('操作成功');
  // 远程删除成功后，同步删除本地数据
  goodsForm.value.skuList = goodsForm.value.skuList.filter((item) => !pidList.includes(item.pid));
  // 刷新表格数据
  await skuGridApi.grid.reloadData(goodsForm.value.skuList);
};
const batchChangeStatusSku = (status: 0 | 1) => {
  const res = skuGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList: string[] = [];
  res.forEach((item) => {
    pidList.push(item.pid);
  });
  const operation = {
    1: {
      label: '启用',
      api: enableSkuApi,
    },
    0: {
      label: '禁用',
      api: disableSkuApi,
    },
  };
  Modal.confirm({
    title: `确认${operation[status].label}`,
    content: `确认${operation[status].label}此SKU吗？`,
    async onOk() {
      await operation[status].api(pidList);
      message.success('操作成功');
      res.forEach((item) => {
        item.status = status;
      });
      skuGridApi.grid.reloadData(goodsForm.value.skuList);
      // await skuGridApi.reload();
    },
  });
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="商品信息" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="goodsForm"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="商品编码" name="spuCode">
            <Input v-model:value="goodsForm.spuCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="商品名称" name="spuName">
            <Input v-model:value="goodsForm.spuName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="商品分类" name="categoryId">
            <ApiComponent
              v-model="goodsForm.categoryId"
              :component="Select"
              :api="getTaxonomyListApi"
              label-field="categoryName"
              value-field="pid"
              model-prop-name="value"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="规格型号" name="specificationModel">
            <Input v-model:value="goodsForm.specificationModel" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="计量单位" name="measureUnit">
            <ApiComponent
              v-model="goodsForm.measureUnit"
              :component="Select"
              :api="getAttributeUnitList"
              label-field="unitName"
              value-field="pid"
              model-prop-name="value"
            />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="商品规格" />
      <Row class="mt-5">
        <Col :span="24">
          <Card v-for="(item, index) in goodsForm.spuSpecList" :key="index" class="mb-4">
            <template #title>
              <div class="w-[400px]">
                <FormItem label="规格名" class="mb-0">
                  <AutoComplete
                    v-model:value="item.skuName"
                    :options="specList"
                    @select="(value, option) => selectSpec(value, option, item)"
                  />
                </FormItem>
              </div>
            </template>
            <template #extra>
              <Button type="link" @click="delSku(index)">删除规格</Button>
            </template>
            <a-tag
              v-for="(tag, index) in item.specValueList"
              :key="tag + index"
              closable
              color="blue"
              @close="handleDeleteSpecValue(tag, item.specValueList)"
            >
              {{ tag }}
            </a-tag>
            <template v-if="item">
              <a-input
                v-if="state.inputVisible"
                ref="inputRef"
                v-model:value="state.inputValue"
                type="text"
                size="small"
                :style="{ width: '78px' }"
                @blur="handleInputConfirm(item)"
                @keyup.enter="handleInputConfirm(item)"
              />
              <a-tag v-else class="cursor-pointer" @click="showInput">
                <PlusOutlined />
                添加
              </a-tag>
            </template>
          </Card>
        </Col>
        <Col :span="24">
          <div class="mt-2">
            <Button type="primary" @click="addSku">添加规格</Button>
          </div>
          <div class="mt-2">
            <Space>
              <Button type="primary" @click="createSku">生成SKU</Button>
              <Button type="primary" danger @click="batchDelNewSku">批量删除</Button>
              <Button type="primary" @click="batchChangeStatusNewSku(1)">启用</Button>
              <Button type="primary" @click="batchChangeStatusNewSku(0)">禁用</Button>
            </Space>
          </div>
        </Col>
        <Col :span="24">
          <NewSkuGrid>
            <template #edit_sku_name="{ row }">
              <Input v-model:value="row.skuName" placeholder="请输入SKU名称" />
            </template>
            <template #edit_sku_code="{ row }">
              <Input v-model:value="row.skuCode" placeholder="请输入SKU编码" />
            </template>
            <template #action="{ row }">
              <Space>
                <TypographyLink v-if="row.status === 0" @click="row.status = 1">启用</TypographyLink>
                <TypographyLink v-else type="danger" @click="row.status = 0">禁用</TypographyLink>
              </Space>
            </template>
          </NewSkuGrid>
        </Col>
      </Row>
      <BasicCaption content="已有商品规格" />
      <Row class="mt-5">
        <Col :span="24">
          <Space>
            <Button type="primary" danger @click="batchDelSku">批量删除</Button>
            <Button type="primary" @click="batchChangeStatusSku(1)">启用</Button>
            <Button type="primary" @click="batchChangeStatusSku(0)">禁用</Button>
          </Space>
        </Col>
        <Col :span="24">
          <SkuGrid>
            <template #edit_sku_name="{ row }">
              <Input v-model:value="row.skuName" placeholder="请输入SKU名称" />
            </template>
            <template #edit_sku_code="{ row }">
              <Input v-model:value="row.skuCode" placeholder="请输入SKU编码" />
            </template>
            <template #action="{ row }">
              <Space>
                <TypographyLink v-if="row.status === 0" @click="changeSkuStatus(row, 1)">启用</TypographyLink>
                <TypographyLink v-else type="danger" @click="changeSkuStatus(row, 0)">禁用</TypographyLink>
              </Space>
            </template>
          </SkuGrid>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
