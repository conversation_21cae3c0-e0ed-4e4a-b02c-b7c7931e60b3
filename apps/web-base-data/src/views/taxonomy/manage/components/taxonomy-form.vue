<script setup lang="ts">
import type { TaxonomyInfo } from '#/api';

import { ref } from 'vue';

import { defaultsDeep } from 'lodash-es';

import { getTaxonomyTreeListApi } from '#/api';

const treeData = ref<TaxonomyInfo[]>([]);

const getTreeList = async () => {
  const res = await getTaxonomyTreeListApi();
  treeData.value = [
    {
      categoryName: '顶级',
      pid: 0,
      children: res,
    },
  ];
};
getTreeList();
const formModel = ref<Partial<TaxonomyInfo>>({
  parentId: 0,
  categoryName: '',
});
const rules = {
  parentId: [{ required: true, message: '上级商品分类' }],
  categoryName: [{ required: true, message: '商品分类名称' }],
};
const formModelRef = ref();
const init = (data: Partial<TaxonomyInfo>) => {
  formModelRef.value.clearValidate();
  formModel.value = defaultsDeep(data, {});
};
const submit = () => {
  formModelRef.value.validate();
  return formModel.value;
};
defineExpose({ init, submit });
</script>

<template>
  <a-form ref="formModelRef" :model="formModel" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
    <a-form-item label="上级商品分类" name="parentId">
      <a-tree-select
        v-model:value="formModel.parentId"
        :tree-data="treeData"
        :field-names="{ label: 'categoryName', value: 'pid' }"
      />
    </a-form-item>
    <a-form-item label="商品分类名称" name="categoryName">
      <a-input v-model:value="formModel.categoryName" />
    </a-form-item>
    <a-form-item label="排序" name="sort">
      <a-input-number v-model:value="formModel.sort" :min="0" :step="1" :precision="0" class="w-full" />
    </a-form-item>
  </a-form>
</template>

<style></style>
