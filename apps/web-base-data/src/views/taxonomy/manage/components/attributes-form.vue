<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { TaxonomyInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent, useVbenModal } from '@vben/common-ui';
import { BasicTitle } from '@vben/fe-ui';

import { Select } from 'ant-design-vue';
import { cloneDeep, defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getAttributeBrandList,
  getAttributeManufacturerList,
  getAttributeUnitList,
  getTaxonomyAttrInfoApi,
} from '#/api';
import EditAttrModal from '#/views/taxonomy/manage/components/edit-attr-modal.vue';

interface AttrInfo {
  attrName: string;
  formType: string;
  attrValueJson: string;
}

interface AttributesInfo {
  categoryCode: string;
  categoryName: string;
  defaultBrandId: string;
  defaultManufacturerId: string;
  defaultMeasureUnit: string;
  attributeList: AttrInfo[];
}

const dataForm = ref<Partial<AttributesInfo>>({});
const init = async (data: TaxonomyInfo) => {
  delete data.children;
  const res = await getTaxonomyAttrInfoApi({ categoryId: data.pid as number });
  dataForm.value = defaultsDeep(data, res, { attributeList: [] });
};
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', dragSort: true },
    { field: 'attrName', title: '属性名称' },
    {
      field: 'formType',
      title: '表单方式',
      minWidth: '120px',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'formType',
        },
      },
    },
    { field: 'attrValueJson', title: '属性值' },
    { field: '', title: '操作', slots: { default: 'action' } },
  ],
  data: dataForm.value.attributeList,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
};
const [Modal, modalApi] = useVbenModal({
  connectedComponent: EditAttrModal,
});
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const addAttr = () => {
  modalApi.setData({ insert: gridApi.grid.insert }).open();
};
const editAttr = (row: AttrInfo) => {
  modalApi.setData({ data: row, insert: gridApi.grid.insert }).open();
};
const delAttr = (row: AttrInfo) => {
  gridApi.grid.remove(row);
};

const dataFormRef = ref();
const submit = async () => {
  await dataFormRef.value.validate();
  dataForm.value.attributeList = gridApi.grid.getTableData().tableData;
  return cloneDeep(dataForm.value) as AttributesInfo;
};
defineExpose({
  init,
  submit,
});
</script>

<template>
  <div>
    <a-form ref="dataFormRef" :model="dataForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :colon="false">
      <BasicTitle>分类信息</BasicTitle>
      <a-row>
        <a-col :span="12">
          <a-form-item label="分类编码">{{ dataForm.categoryCode }}</a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="分类名称">{{ dataForm.categoryName }}</a-form-item>
        </a-col>
      </a-row>
      <BasicTitle>通用属性</BasicTitle>
      <a-row>
        <a-col :span="12">
          <a-form-item label="商品品牌">
            <ApiComponent
              v-model="dataForm.defaultBrandId"
              :component="Select"
              :api="getAttributeBrandList"
              label-field="brandName"
              value-field="pid"
              model-prop-name="value"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="生产厂商">
            <ApiComponent
              v-model="dataForm.defaultManufacturerId"
              :component="Select"
              :api="getAttributeManufacturerList"
              label-field="manufacturerName"
              value-field="pid"
              model-prop-name="value"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="计量单位">
            <ApiComponent
              v-model="dataForm.defaultMeasureUnit"
              :component="Select"
              :api="getAttributeUnitList"
              label-field="unitName"
              value-field="pid"
              model-prop-name="value"
              number-to-string
            />
          </a-form-item>
        </a-col>
      </a-row>
      <BasicTitle>基础属性</BasicTitle>
      <Grid>
        <template #toolbar-actions>
          <a-button class="mr-2" type="primary" @click="addAttr">新增属性</a-button>
        </template>
        <template #action="{ row }">
          <a-space>
            <a-typography-link @click="editAttr(row)">编辑</a-typography-link>
            <a-typography-link type="danger" @click="delAttr(row)">删除</a-typography-link>
          </a-space>
        </template>
      </Grid>
    </a-form>
    <Modal />
  </div>
</template>

<style></style>
