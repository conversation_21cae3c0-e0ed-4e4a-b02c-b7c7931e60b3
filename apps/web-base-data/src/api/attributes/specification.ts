import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export async function getSpecificationPageListApi(params: PageListParams) {
  return requestClient.get('/scm-new/specTpl/page', { params });
}
export async function addSpecificationApi(data) {
  return requestClient.post('/scm-new/specTpl/add', data);
}
export async function editSpecificationApi(data) {
  return requestClient.post('/scm-new/specTpl/update', data);
}
export async function deleteSpecificationApi(data) {
  return requestClient.post('/scm-new/specTpl/delete', data);
}
