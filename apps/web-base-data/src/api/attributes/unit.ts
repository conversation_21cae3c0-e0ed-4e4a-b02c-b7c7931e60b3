import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface UnitInfo extends BaseDataParams {
  // 单位名称
  unitName: string;
  // 备注
  remarks: string;
}

export async function getAttributeUnitPageList(params: PageListParams) {
  return requestClient.get('/scm-new/unit/page', { params });
}
export async function getAttributeUnitList(params: PageListParams) {
  return requestClient.get('/scm-new/unit/list', { params });
}
export async function addAttributeUnitApi(data: UnitInfo) {
  return requestClient.post('/scm-new/unit/add', data);
}
export async function updateAttributeUnitApi(data: UnitInfo) {
  return requestClient.post('/scm-new/unit/update', data);
}
export async function deleteAttributeUnitApi(ids: number[]) {
  return requestClient.post('/scm-new/unit/delete', ids);
}
export async function enableAttributeUnitApi(ids: number[]) {
  return requestClient.post('/scm-new/unit/enable', ids);
}
export async function disableAttributeUnitApi(ids: number[]) {
  return requestClient.post('/scm-new/unit/disable', ids);
}
