import { requestClient } from '#/api/request';

export async function editSkuApi(data) {
  return requestClient.post('/scm-new/sku/update', data);
}
export async function enableSkuApi(data: string[]) {
  return requestClient.post('/scm-new/sku/enable', data);
}
export async function disableSku<PERSON><PERSON>(data: string[]) {
  return requestClient.post('/scm-new/sku/disable', data);
}
export async function deleteSkuApi(data: string[]) {
  return requestClient.post('/scm-new/sku/delete', data);
}
