import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface SpuInfo extends BaseDataParams {
  pid: string;
  spuSpecList: {
    specName: string;
  }[];
  newSkuList: [];
  skuList: {
    specJson: string;
  }[];
  spuName: string;
  spuCode: string;
  categoryId: string;
  specificationModel: string;
  measureUnit: string;
}

export async function getSpuPageListApi(params: PageListParams) {
  return requestClient.get('/scm-new/spu/page', { params });
}
export async function addSpuApi(data) {
  return requestClient.post('/scm-new/spu/add', data);
}
export async function editSpuApi(data) {
  return requestClient.post('/scm-new/spu/update', data);
}
export async function deleteSpuApi(data) {
  return requestClient.post('/scm-new/spu/delete', data);
}
export async function getSpuInfoApi(params) {
  return requestClient.get('/scm-new/spu/info', { params });
}
