<script setup lang="ts">
import type { AppInfo } from '#/api';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

const appData = ref<Partial<AppInfo>>({});
const [Modal, modalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      appData.value = modalApi.getData();
    }
  },
});
</script>

<template>
  <Modal :show-confirm-button="false" cancel-text="关闭" class="w-[600px]" title="应用详情">
    <Descriptions :column="1">
      <DescriptionsItem label="应用编码">{{ appData.code }}</DescriptionsItem>
      <DescriptionsItem label="应用名称">{{ appData.name }}</DescriptionsItem>
      <DescriptionsItem label="应用类型">
        {{ appData.appType }}
      </DescriptionsItem>
      <DescriptionsItem label="应用图标">
        <div class="flex h-full items-center">
          <VbenIcon :icon="appData.icon" />
        </div>
      </DescriptionsItem>
      <DescriptionsItem label="访问地址">{{ appData.url }}</DescriptionsItem>
      <DescriptionsItem label="应用排序">{{ appData.sort }}</DescriptionsItem>
    </Descriptions>
  </Modal>
</template>

<style></style>
