<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AiSecretKeyInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import {
  Modal as AntdModal,
  Button,
  Form,
  FormItem,
  Input,
  message,
  Select,
  Space,
  Switch,
  TypographyLink,
} from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addAiSecretKeyApi, deleteAiSecretKeyApi, editAiSecretKeyApi, getAiSecretKeyPageListApi } from '#/api';

const { getDictList } = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: '名称',
    },
    {
      component: 'DictSelect',
      fieldName: 'platform',
      label: '模型供应商',
      componentProps: {
        code: 'AI_PLATFORM',
      },
    },
    {
      component: 'Select',
      fieldName: 'enabled',
      label: '状态',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: true,
  },
});
const gridOptions: VxeTableGridOptions<AiSecretKeyInfo> = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    {
      field: 'platform',
      title: '模型提供商',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'AI_PLATFORM',
        },
      },
    },
    { field: 'name', title: '名称' },
    { field: 'apiKey', title: 'API KEY' },
    { field: 'url', title: 'API URL' },
    {
      field: 'enabled',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAiSecretKeyPageListApi({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const loading = ref({
  save: false,
});
const secretKeyFormRef = ref();
const modalTitle = ref('新建秘钥');
const defaultSecretKeyInfo: Partial<AiSecretKeyInfo> = {
  enabled: 1,
};
const secretKeyForm = ref<Partial<AiSecretKeyInfo>>(defaultsDeep(defaultSecretKeyInfo));
const rules: Record<string, Rule[]> = {
  platform: [{ required: true, message: '请选择模型提供商', trigger: 'change' }],
  name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
  enabled: [{ required: true, message: '状态', trigger: 'change' }],
  url: [{ required: true, message: 'API URL', trigger: 'change' }],
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await secretKeyFormRef.value.validate();
    loading.value.save = true;
    let api = addAiSecretKeyApi;
    if (secretKeyForm.value.id) {
      api = editAiSecretKeyApi;
    }
    try {
      await api(secretKeyForm.value as AiSecretKeyInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.save = false;
    }
  },
  onBeforeClose: () => {
    secretKeyForm.value = defaultsDeep(defaultSecretKeyInfo);
    secretKeyFormRef.value.resetFields();
    apiKeyView.value = '';
    return true;
  },
});
const addModel = () => {
  modalTitle.value = '新建秘钥';
  modalApi.open();
};
const apiKeyView = ref();
const edit = (row: AiSecretKeyInfo) => {
  modalTitle.value = '编辑秘钥';
  const formData = defaultsDeep(row, defaultSecretKeyInfo);
  apiKeyView.value = row.apiKey;
  delete formData.apiKey;
  secretKeyForm.value = formData;
  modalApi.open();
};
const del = (row: AiSecretKeyInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    onOk: async () => {
      await deleteAiSecretKeyApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="addModel">新建</Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal :title="modalTitle" :submitting="loading.save">
      <Form
        ref="secretKeyFormRef"
        :model="secretKeyForm"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        :colon="false"
        :rules="rules"
        autocomplete="off"
      >
        <FormItem name="platform" label="模型提供商">
          <Select v-model:value="secretKeyForm.platform" :options="getDictList('AI_PLATFORM')" />
        </FormItem>
        <FormItem name="name" label="名称">
          <Input v-model:value="secretKeyForm.name" />
        </FormItem>
        <FormItem name="url" label="API URL">
          <Input v-model:value="secretKeyForm.url" />
        </FormItem>
        <FormItem name="apiKey" label="API KEY">
          <Input v-model:value="secretKeyForm.apiKey" :placeholder="apiKeyView" />
        </FormItem>
        <FormItem prop="enabled" label="状态">
          <Switch v-model:checked="secretKeyForm.enabled" :checked-value="1" :un-checked-value="0" />
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
