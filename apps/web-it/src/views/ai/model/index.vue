<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AiModelInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import {
  Modal as AntdModal,
  Button,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Select,
  Space,
  Switch,
  TypographyLink,
} from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addAiModelApi, deleteAiModelApi, editAiModelApi, getAiModelPageListApi, getAiSecretKeyListApi } from '#/api';

const { getDictList } = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: '名称',
    },
    {
      component: 'DictSelect',
      fieldName: 'platform',
      label: '模型供应商',
      componentProps: {
        code: 'AI_PLATFORM',
      },
    },
    {
      component: 'Select',
      fieldName: 'enabled',
      label: '状态',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: true,
  },
});
const gridOptions: VxeTableGridOptions<AiModelInfo> = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    {
      field: 'platform',
      title: '模型提供商',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'AI_PLATFORM',
        },
      },
    },
    {
      field: 'type',
      title: '模型类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'AI_MODEL_TYPE',
        },
      },
    },
    { field: 'name', title: '模型名称' },
    { field: 'model', title: '模型标识' },
    { field: 'sortCode', title: '排序' },
    {
      field: 'enabled',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAiModelPageListApi({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const loading = ref({
  save: false,
});
const modelFormRef = ref();
const modalTitle = ref('新建模型');
const defaultModelInfo: Partial<AiModelInfo> = {
  enabled: 1,
  sortCode: 0,
  temperature: 0.7,
};
const modelForm = ref<Partial<AiModelInfo>>(defaultsDeep(defaultModelInfo));
const rules: Record<string, Rule[]> = {
  platform: [{ required: true, message: '请选择模型提供商', trigger: 'change' }],
  type: [{ required: true, message: '请选择模型类型', trigger: 'change' }],
  keyId: [{ required: true, message: '请选择API KEY', trigger: 'change' }],
  name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
  model: [{ required: true, message: '请输入模型标识', trigger: 'blur' }],
  temperature: [{ required: true, message: '请输入模型温度', trigger: 'blur' }],
  maxTokens: [{ required: true, message: '请输入单回复最大Token数', trigger: 'blur' }],
  maxContexts: [{ required: true, message: '请输入上下文最大消息量', trigger: 'blur' }],
  enabled: [{ required: true, message: '状态', trigger: 'change' }],
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await modelFormRef.value.validate();
    loading.value.save = true;
    let api = addAiModelApi;
    if (modelForm.value.id) {
      api = editAiModelApi;
    }
    try {
      await api(modelForm.value as AiModelInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.save = false;
    }
  },
  onBeforeClose: () => {
    modelForm.value = defaultsDeep(defaultModelInfo);
    modelFormRef.value.resetFields();
    return true;
  },
});
const addModel = () => {
  modalTitle.value = '新建模型';
  modalApi.open();
};
const edit = (row: AiModelInfo) => {
  modalTitle.value = '编辑模型';
  modelForm.value = defaultsDeep(row, defaultModelInfo);
  modalApi.open();
};
const del = (row: AiModelInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    onOk: async () => {
      await deleteAiModelApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const aiSecretKeyList = ref([]);
const getAiServiceList = async () => {
  aiSecretKeyList.value = await getAiSecretKeyListApi();
};
getAiServiceList()
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="addModel">新建</Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal :title="modalTitle" :submitting="loading.save">
      <Form
        ref="modelFormRef"
        :model="modelForm"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        :colon="false"
        :rules="rules"
        autocomplete="off"
      >
        <FormItem name="platform" label="模型提供商">
          <Select v-model:value="modelForm.platform" :options="getDictList('AI_PLATFORM')" />
        </FormItem>
        <FormItem name="type" label="模型类型">
          <Select v-model:value="modelForm.type" :options="getDictList('AI_MODEL_TYPE')" />
        </FormItem>
        <FormItem name="name" label="模型名称">
          <Input v-model:value="modelForm.name" />
        </FormItem>
        <FormItem name="model" label="模型标识">
          <Input v-model:value="modelForm.model" />
        </FormItem>
        <FormItem name="keyId" label="API KEY">
          <Select v-model:value="modelForm.keyId" :options="aiSecretKeyList" :field-names="{ label: 'name', value: 'id' }" />
        </FormItem>
        <FormItem name="temperature" label="模型温度">
          <InputNumber v-model:value="modelForm.temperature" :min="0" class="w-full" />
        </FormItem>
        <FormItem name="maxTokens" label="最大响应Tokens">
          <InputNumber v-model:value="modelForm.maxTokens" :min="0" :step="1" :precision="0" class="w-full" />
        </FormItem>
        <FormItem name="maxContexts" label="上下文数量">
          <InputNumber v-model:value="modelForm.maxContexts" :min="0" :step="1" :precision="0" class="w-full" />
        </FormItem>
        <FormItem label="排序">
          <InputNumber
            v-model:value="modelForm.sortCode"
            :controls="false"
            :min="0"
            :precision="0"
            :step="1"
            class="w-full"
          />
        </FormItem>
        <FormItem prop="enabled" label="状态">
          <Switch v-model:checked="modelForm.enabled" :checked-value="1" :un-checked-value="0" />
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
