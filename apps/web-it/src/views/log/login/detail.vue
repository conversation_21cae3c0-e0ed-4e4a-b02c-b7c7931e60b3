<script setup lang="ts">
import type { LogSystemInfo } from '#/api';

import { ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import { Card } from 'ant-design-vue';

import { getLogDetailApi } from '#/api';
import DynamicDescriptions from '#/components/dynamic-descriptions.vue';

const operatorSchema = [
  {
    fieldName: 'userName',
    label: '用户',
  },
  {
    fieldName: 'account',
    label: '账号',
  },
  {
    fieldName: 'type',
    label: '类型',
  },
  {
    fieldName: 'loginTime',
    label: '登录时间',
  },
  {
    fieldName: 'platform',
    label: '登录平台',
  },
  {
    fieldName: 'method',
    label: '登录方式',
  },
  {
    fieldName: 'remoteIp',
    label: '登录IP',
  },
  {
    fieldName: 'remoteAddress',
    label: '登录地址',
  },
  {
    fieldName: 'os',
    label: '系统',
  },
  {
    fieldName: 'client',
    label: '客户端',
  },
  {
    fieldName: 'device',
    label: '设备',
    span: 2,
  },
  {
    fieldName: 'userAgent',
    label: '用户代理',
    span: 3,
  },
];

const logDetail = ref<Partial<LogSystemInfo>>({});
const route = useRoute();
const getDetail = async () => {
  logDetail.value = await getLogDetailApi({ pid: route.query.pid as string });
};
getDetail();
</script>

<template>
  <Page
    :title="logDetail.title ?? '审计日志详情'"
    auto-content-height
    show-back
  >
    <Card title="操作人">
      <DynamicDescriptions :data="logDetail" :schema="operatorSchema" />
    </Card>
  </Page>
</template>

<style>
json-viewer {
  --font-size: 0.8rem;
}
</style>
