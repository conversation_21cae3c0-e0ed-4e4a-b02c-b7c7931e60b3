<script setup lang="ts">
import type { LogSystemInfo } from '#/api';

import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import { JsonViewer, Page } from '@vben/common-ui';
import { formatDate } from '@vben/utils';

import { Card, Col, Row } from 'ant-design-vue';

import { getLogDetailApi } from '#/api';
import DynamicDescriptions from '#/components/dynamic-descriptions.vue';

const operatorSchema = [
  {
    fieldName: 'userName',
    label: '用户',
  },
  {
    fieldName: 'account',
    label: '账号',
  },
  {
    fieldName: 'remoteIp',
    label: '请求IP',
  },
  {
    fieldName: 'remoteAddress',
    label: '请求地址',
  },
  {
    fieldName: 'os',
    label: '系统',
  },
  {
    fieldName: 'client',
    label: '客户端',
  },
  {
    fieldName: 'device',
    label: '设备',
  },
  {
    fieldName: 'userAgent',
    label: '用户代理',
    span: 2,
  },
];
const methodSchema = [
  {
    fieldName: 'method',
    label: 'Method',
  },
  {
    fieldName: 'status',
    label: 'Status',
  },
  {
    fieldName: 'operateTime',
    label: '请求时间',
    formatter: formatDate,
  },
  {
    fieldName: 'time',
    label: '消耗时长',
  },
  {
    fieldName: 'serviceId',
    label: '服务ID',
  },
  {
    fieldName: 'serverIp',
    label: '服务器IP',
  },
  {
    fieldName: 'serverHost',
    label: '服务器名',
  },
  {
    fieldName: 'env',
    label: '环境',
  },
  {
    fieldName: 'platform',
    label: '操作平台',
  },
  {
    fieldName: 'type',
    label: '操作类型',
  },
  {
    fieldName: 'title',
    label: '操作内容',
  },
];

const logDetail = ref<Partial<LogSystemInfo>>({});
const route = useRoute();
const getDetail = async () => {
  logDetail.value = await getLogDetailApi({ pid: route.query.pid as string });
};
const reqJson = computed(() => {
  return logDetail.value.request
    ? JSON.parse(logDetail.value.request)
    : undefined;
});
const resJson = computed(() => {
  return logDetail.value.response
    ? JSON.parse(logDetail.value.response)
    : undefined;
});
getDetail();
</script>

<template>
  <Page
    :title="logDetail.title ?? '审计日志详情'"
    auto-content-height
    show-back
  >
    <Card title="操作人">
      <DynamicDescriptions :data="logDetail" :schema="operatorSchema" />
    </Card>
    <Card title="操作方式" class="mt-4">
      <DynamicDescriptions :data="logDetail" :schema="methodSchema" />
    </Card>
    <Card title="请求路径" class="mt-4">
      <JsonViewer :value="logDetail.requestUrl" class="rounded-md px-4 py-2" />
      <!--<json-viewer :data="logDetail.requestUrl" class="rounded-md px-4 py-2" />-->
    </Card>
    <Row :gutter="16" class="mt-4">
      <Col :span="12">
        <Card title="请求消息">
          <JsonViewer
            :value="reqJson"
            :show-array-index="false"
            preview-mode
            boxed
            class="rounded-md px-4 py-2"
          />
        </Card>
      </Col>
      <Col :span="12">
        <Card title="响应消息">
          <JsonViewer
            :value="resJson"
            :show-array-index="false"
            preview-mode
            boxed
            class="rounded-md px-4 py-2"
          />
        </Card>
      </Col>
    </Row>
  </Page>
</template>

<style>
json-viewer {
  --font-size: 0.8rem;
}
</style>
