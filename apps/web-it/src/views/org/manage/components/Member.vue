<script lang="ts" setup>
import type { ScrollActionType } from '#/components/Container';

import { reactive, ref, toRefs } from 'vue';

import { BasicModal, FeEmpty, ScrollContainer, useModalInner } from '@vben/fe-ui';

import { Avatar, InputSearch } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { getUserIdsByRelationApi, getUserInfoByIdsApi } from '#/api';

interface State {
  list: any[];
  listQuery: any;
  loading: boolean;
  finish: boolean;
  total: number;
  title: string;
}

defineEmits(['register']);

const [registerModal] = useModalInner(init);
const infiniteBody = ref<Nullable<ScrollActionType>>(null);
const state = reactive<State>({
  list: [],
  localList: [],
  listQuery: {
    objectId: '',
    objectType: '',
    currentPage: 1,
    pageSize: 50,
    sort: 'desc',
    sidx: '',
    keyword: '',
    enabledMark: 1,
  },
  loading: false,
  finish: false,
  total: 0,
  title: '',
});
const { list, listQuery, loading, title, localList } = toRefs(state);

function init(data) {
  state.title = `查看成员 - ${data.name ?? ''}`;
  state.listQuery.objectId = data.id;
  state.listQuery.objectType = data.objectType;
  state.listQuery.keyword = '';
  state.list = [];
  state.localList = [];
  initData();
  // nextTick(() => {
  //   bindScroll();
  // });
}
async function initData() {
  state.loading = true;
  const userIds = await getUserIdsByRelationApi({
    id: state.listQuery.objectId,
    type: state.listQuery.objectType,
  });
  const list = await getUserInfoByIdsApi(userIds);
  state.list = cloneDeep(list);
  state.localList = cloneDeep(list);
  state.loading = false;
}
function bindScroll() {
  const bodyRef = infiniteBody.value;
  const vBody = bodyRef?.getScrollWrap();
  vBody?.addEventListener('scroll', () => {
    if (vBody.scrollHeight - vBody.clientHeight - vBody.scrollTop <= 200 && !state.loading && !state.finish) {
      state.listQuery.currentPage += 1;
      initData();
    }
  });
}
function handleSearch() {
  const query = state.listQuery.keyword;
  const listCopy = cloneDeep(state.list);
  // 如果没有输入查询条件，则直接将原始列表复制到 localList
  if (!query) {
    state.localList = listCopy;
    return;
  }
  // 模糊匹配 fullName 字段
  state.localList = listCopy.filter((item) => {
    return item.fullName.toLowerCase().includes(query.toLowerCase());
  });
  // state.list = [];
  // state.finish = false;
  // state.listQuery.currentPage = 1;
  // state.listQuery.pageSize = 20;
  // initData();
}
</script>
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    :footer="null"
    class="transfer-modal member-modal"
  >
    <div class="transfer__body">
      <div class="transfer-pane left-pane">
        <div class="transfer-pane__tool">
          <InputSearch
            placeholder="请输入姓名/账号"
            allow-clear
            v-model:value="listQuery.keyword"
            @search="handleSearch"
          />
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab">
          <ScrollContainer v-loading="loading && listQuery.currentPage === 1" ref="infiniteBody">
            <div v-for="item in localList" :key="item.id" class="selected-item selected-item-user">
              <div class="selected-item-main">
                <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                <div class="selected-item-text">
                  <p class="name">{{ item.fullName }}</p>
                  <!--<p class="name">{{ item.realName }}/{{ item.account }}</p>-->
                  <p class="organize" :title="item.organize">{{ item.organize }}</p>
                </div>
              </div>
            </div>
            <FeEmpty v-if="list.length === 0" />
          </ScrollContainer>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
