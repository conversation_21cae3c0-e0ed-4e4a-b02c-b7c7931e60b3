<script setup lang="ts">
import { useVbenModal } from '@vben/common-ui';

const props = defineProps({
  code: { type: String, required: true },
});

const [Modal, modalApi] = useVbenModal();
</script>

<template>
  <button
    class="vxe-button type--button size--small is--circle"
    @click="() => modalApi.open()"
  >
    <span class="icon-[pepicons-print--question]"></span>
  </button>
  <Modal
    class="h-[800px] w-[1200px]"
    :show-confirm-button="false"
    cancel-text="关闭"
    title="帮助文档"
  >
    <iframe
      src="http://*************:4000/chat/share?shareId=kitd2frm4kpu3pc9lgpsd81j&showHistory=0&module=融资申请"
      style="width: 100%; height: 100%"
      frameborder="0"
      allow="*"
    ></iframe>
  </Modal>
</template>

<style></style>
