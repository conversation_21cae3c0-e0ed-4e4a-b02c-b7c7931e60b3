<script lang="ts">
import type { PropType } from 'vue';

import { computed, defineComponent } from 'vue';

import { useDesign } from '@vben/fe-ui/hooks/web/useDesign';
import { isString } from '@vben/fe-ui/utils/is';
import { propTypes } from '@vben/fe-ui/utils/propTypes';

import { Image } from 'ant-design-vue';

interface ImageProps {
  alt?: string;
  fallback?: string;
  src: string;
  width: number | string;
  height?: number | string;
  placeholder?: boolean | string;
  preview?:
    | boolean
    | {
        getContainer: (() => HTMLElement) | HTMLElement | string;
        onVisibleChange?: (visible: boolean, prevVisible: boolean) => void;
        visible?: boolean;
      };
}

type ImageItem = ImageProps | string;

export default defineComponent({
  name: 'ImagePreview',
  components: {
    Image,
    PreviewGroup: Image.PreviewGroup,
  },
  props: {
    functional: propTypes.bool,
    imageList: {
      type: Array as PropType<ImageItem[]>,
    },
  },
  setup(props) {
    const { prefixCls } = useDesign('image-preview');

    const getImageList = computed((): any[] => {
      const { imageList } = props;
      if (!imageList) {
        return [];
      }
      return imageList.map((item) => {
        if (isString(item)) {
          return {
            src: item,
            placeholder: false,
          };
        }
        return item;
      });
    });

    return {
      prefixCls,
      getImageList,
    };
  },
});
</script>
<template>
  <div :class="prefixCls">
    <PreviewGroup>
      <slot v-if="!imageList || $slots.default"></slot>
      <template v-else>
        <template v-for="item in getImageList" :key="item.src">
          <Image v-bind="item">
            <template #placeholder v-if="item.placeholder">
              <Image v-bind="item" :src="item.placeholder" :preview="false" />
            </template>
          </Image>
        </template>
      </template>
    </PreviewGroup>
  </div>
</template>
<style lang="less">
@import '#/style/index.less';
@prefix-cls: ~'@{namespace}-image-preview';

.@{prefix-cls} {
  .ant-image {
    margin-right: 10px;
  }

  .ant-image-preview-operations {
    background-color: rgb(0 0 0 / 40%);
  }
}
</style>
