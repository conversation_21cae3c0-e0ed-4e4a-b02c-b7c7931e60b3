<script lang="ts" setup>
import type { TreeActionType } from '#/components/Tree';

import { reactive, ref, toRefs, unref } from 'vue';

import { BasicModal, BasicTree, FeEmpty, ScrollContainer, useModalInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';

import { DeleteOutlined } from '@ant-design/icons-vue';
import { Avatar, InputSearch, message } from 'ant-design-vue';

import {
  getUserIdsByRelationApi,
  getUserInfoByIdsApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
  saveUserRelationApi,
} from '#/api';

interface State {
  treeData: any[];
  dataForm: any;
  title: string;
  keyword: string;
  selectedData: any[];
  nodeId: string;
  loading: boolean;
  isAsync: boolean;
  treeKey: number;
}

defineEmits(['register']);

const treeRef = ref<Nullable<TreeActionType>>(null);
const [registerModal, { changeLoading, changeOkLoading, closeModal }] = useModalInner(init);
const state = reactive<State>({
  treeData: [],
  dataForm: {
    objectId: '',
    objectType: '',
    userIds: [],
  },
  keyword: '',
  title: '',
  selectedData: [],
  nodeId: '-1',
  loading: false,
  isAsync: false,
  treeKey: Date.now(),
});
const { treeData, title, selectedData, keyword, loading, isAsync, treeKey } = toRefs(state);

function init(data) {
  changeLoading(true);
  state.isAsync = false;
  const roleLabel = {
    GROUP: '分组',
    ROLE: '角色',
    POST: '岗位',
    ORGANIZE: '组织',
  };
  state.title = `${roleLabel[data.objectType] ?? ''}成员 - ${data.name ?? ''}`;
  state.dataForm.objectId = data.id;
  state.dataForm.objectType = data.objectType;
  state.dataForm.userIds = [];
  state.treeData = [];
  state.selectedData = [];
  state.keyword = '';
  state.nodeId = '-1';
  getUserIdsByRelationApi({ id: state.dataForm.objectId, type: state.dataForm.objectType }).then((res) => {
    state.dataForm.userIds = res;
    if (state.dataForm.userIds.length === 0) return changeLoading(false);
    getUserInfoByIdsApi(state.dataForm.userIds).then((res) => {
      state.selectedData = res;
      changeLoading(false);
    });
  });
  initData();
}
const iconList = {
  COMPANY: 'clarity:organization-line',
  DEPARTMENT: 'clarity:group-line',
  USER: 'lucide:user',
};
const getTreeList = async (params) => {
  if (params.orgId && params.orgId.includes('_')) {
    const idList = params.orgId.split('_');
    params.orgId = idList[idList.length - 1];
  }
  let api = getUserTreeListApi;
  if (params.keyword) {
    api = getUserListByKeywordApi;
    params.keywords = params.keyword.trim();
  }
  const res = await api(params);
  res.forEach((item) => {
    item.isLeaf = Boolean(item.isLeaf);
    item.icon = iconList[item.type];
    if (item.type !== 'USER') {
      item.id = `${item.type}_${item.id}`;
    }
  });
  return res;
};
function initData() {
  state.loading = true;
  if (state.keyword) state.nodeId = '-1';
  const query = {
    keyword: state.keyword,
    orgId: state.nodeId,
    roleId: state.dataForm.objectId,
  };
  getTreeList(query).then((res) => {
    state.treeData = res;
    state.loading = false;
    if (treeData.value.length > 0 && state.nodeId === '-1') {
      getTree().setExpandedKeys([treeData.value[0].id]);
    }
  });
}
function onLoadData(node) {
  state.nodeId = node.id;
  const query = {
    keyword: state.keyword,
    orgId: state.nodeId,
    roleId: state.dataForm.objectId,
  };
  return new Promise((resolve: (value?: unknown) => void) => {
    getTreeList(query).then((res) => {
      const list = res;
      getTree().updateNodeByKey(node.eventKey, { children: list, isLeaf: list.length === 0 });
      resolve();
    });
  });
}
function handleSelect(keys) {
  if (keys.length === 0) return;
  const data = getTree().getSelectedNode(keys[0]);
  if (data?.type !== 'USER') return;
  handleNodeClick(data);
}
function handleNodeClick(data) {
  const boo = state.selectedData.some((o) => o.id === data.id);
  if (boo) return;
  state.selectedData.push(data);
}
function handleSearch() {
  state.treeKey = Date.now();
  state.nodeId = '-1';
  state.treeData = [];
  state.isAsync = !!state.keyword;
  initData();
}
function getTree() {
  const tree = unref(treeRef);
  if (!tree) throw new Error('tree is null!');
  return tree;
}
function removeAll() {
  state.selectedData = [];
}
function removeData(index: number) {
  state.selectedData.splice(index, 1);
}
function handleSubmit() {
  changeOkLoading(true);
  state.dataForm.userIds = state.selectedData.map((o) => o.id);
  saveUserRelationApi(state.dataForm)
    .then(() => {
      message.success($t('base.resSuccess'));
      changeOkLoading(false);
      closeModal();
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    :width="800"
    @ok="handleSubmit"
    destroy-on-close
    class="transfer-modal"
  >
    <div class="transfer__body">
      <div class="transfer-pane left-pane">
        <div class="transfer-pane__tool">
          <InputSearch placeholder="请输入姓名" allow-clear v-model:value="keyword" @search="handleSearch" />
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab">
          <BasicTree
            class="tree-main"
            :tree-data="treeData"
            :load-data="onLoadData"
            click-row-to-expand
            @select="handleSelect"
            ref="treeRef"
            :loading="loading"
            :field-names="{ title: 'fullName' }"
            :key="treeKey"
            v-if="!isAsync"
          />
          <ScrollContainer v-loading="loading" v-else>
            <div
              v-for="item in treeData"
              :key="item.id"
              class="selected-item selected-item-user"
              @click="handleNodeClick(item)"
            >
              <div class="selected-item-main">
                <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                <div class="selected-item-text">
                  <p class="name">{{ item.fullName }}</p>
                  <p class="organize" :title="item.organize">{{ item.organize }}</p>
                </div>
              </div>
            </div>
            <FeEmpty v-if="treeData.length === 0" />
          </ScrollContainer>
        </div>
      </div>
      <div class="transfer-pane right-pane">
        <div class="transfer-pane__tool">
          <span>已选</span>
          <span class="remove-all-btn" @click="removeAll">清空列表</span>
        </div>
        <div class="transfer-pane__body">
          <ScrollContainer>
            <div v-for="(item, i) in selectedData" :key="i" class="selected-item selected-item-user">
              <div class="selected-item-main">
                <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                <div class="selected-item-text">
                  <p class="name">{{ item.fullName }}</p>
                  <p class="organize" :title="item.organize">{{ item.organize }}</p>
                </div>
                <DeleteOutlined class="delete-btn" @click="removeData(i)" />
              </div>
            </div>
            <FeEmpty v-if="selectedData.length === 0" />
          </ScrollContainer>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
