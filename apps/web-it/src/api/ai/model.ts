import type { BaseDataParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface AiModelInfo extends BaseDataParams {
  // 可选, 关联的API密钥ID，对应ApiKeyEntity的主键
  keyId?: number;
  // 可选, 模型显示名称
  name?: string;
  // 可选, 模型唯一标识符（平台定义的模型标识）
  model?: string;
  // 可选, 所属平台
  platform?:
    | 'DEEP_SEEK'
    | 'DOU_BAO'
    | 'HUN_YUAN'
    | 'MINI_MAX'
    | 'MOONSHOT'
    | 'OPENAI'
    | 'SILICON_FLOW'
    | 'TONG_YI'
    | 'XING_HUO'
    | 'YI_YAN'
    | 'ZHI_PU';
  // 可选, 模型类型
  type?: 'EMBEDDING' | 'LLM' | 'RERANK' | 'STT' | 'TTS';
  // 可选, 排序码
  sortCode?: number;
  // 可选, 启用状态（0-禁用 1-启用）
  enabled?: 0 | 1;
  // 可选, 温度参数
  temperature?: number;
  // 可选, 单条回复允许的最大Token数量
  maxTokens?: number;
  // 可选, 上下文保留的最大消息数量
  maxContexts?: number;
}

export async function getAiModelPageListApi(params: { page: string }) {
  return requestClient.get('/ai/model/page', { params });
}
export async function getAiModelListApi(params: { type: string }) {
  return requestClient.get('/ai/model/simple-list', { params });
}
export async function addAiModelApi(data: AiModelInfo) {
  return requestClient.post('/ai/model/create', data);
}
export async function editAiModelApi(data: AiModelInfo) {
  return requestClient.post('/ai/model/update', data);
}
export async function deleteAiModelApi(id: string) {
  return requestClient.post('/ai/model/delete', {}, { params: { id } });
}
