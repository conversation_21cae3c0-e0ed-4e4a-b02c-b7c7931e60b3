import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface GroupInfo extends BaseDataParams {
  // 编码
  code: string;
  // 名称
  name: string;
  // 描述
  description?: string;
  // 排序
  sortCode?: number;
  // 是否启用
  enabled?: number;
  // 扩展属性
  propertyJson?: string;
}

export function getGroupPageListApi(params: PageListParams) {
  return requestClient.get('/upms/group/page', { params });
}
export function addGroupApi(data: GroupInfo) {
  return requestClient.post('/upms/group/add', data);
}
export function editGroupApi(data: GroupInfo) {
  return requestClient.post('/upms/group/edit', data);
}
export function deleteGroupApi(id: string) {
  return requestClient.post('/upms/group/delete', {}, { params: { id } });
}
