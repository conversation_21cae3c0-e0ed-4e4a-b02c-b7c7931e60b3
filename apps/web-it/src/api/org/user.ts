import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// 性别
export enum Gender {
  Female = 'FEMALE',
  Male = 'MALE',
  Secret = 'SECRET',
}
export interface UserInfo extends BaseDataParams {
  // 账号
  account: string;
  // 编码
  code?: string;
  // 邮箱
  email?: string;
  // 过期日期；NULL=永不过期
  expiryDate?: Date;
  // 性别
  gender?: Gender;
  // 分组主键
  groupIds?: string[];
  // 头像
  headIcon?: string;
  // 直属上级主键
  managerId?: string;
  // 电话
  mobile?: string;
  // 组织主键
  organizeIds?: string[];
  // 岗位主键
  postIds?: string[];
  // 扩展属性
  propertyJson?: string;
  // 姓名
  realName: string;
  // 角色主键
  roleIds?: string[];
  // 是否启用; -1=禁用 0=锁定 1=启用
  status?: number;
  // 排序
  sortCode?: number;
  // 说明
  description?: string;
}
export async function getUserPageListApi(params: PageListParams) {
  return requestClient.get<UserInfo[]>('/upms/user/page', { params });
}
export async function addUserApi(data: UserInfo) {
  return requestClient.post<UserInfo>('/upms/user/add', data);
}
export async function editUserApi(data: UserInfo) {
  return requestClient.post<UserInfo>('/upms/user/edit', data);
}
export async function delUserApi(id: string) {
  return requestClient.post('/upms/user/delete', {}, { params: { id } });
}
export async function setUserPasswordApi(data: { id: string; newPassword: string }) {
  return requestClient.post('/upms/user/update_pwd', {}, { params: data });
}
export async function lockUserApi(userIds: string[]) {
  return requestClient.post('/upms/user/lock', userIds);
}
export async function unlockUserApi(userIds: string[]) {
  return requestClient.post('/upms/user/unlock', userIds);
}
export async function getUserTreeListApi(params: { name: string; orgId: string }) {
  return requestClient.post('/upms/user/async/selector_by_org_id', {}, { params });
}
export async function getUserListByKeywordApi(params: { keyword: string }) {
  return requestClient.post('/upms/user/selector_by_keywords', {}, { params });
}
export async function getUserInfoByIdsApi(ids: string[]) {
  return requestClient.post('/upms/user/selector_by_ids', ids);
}
export async function getUserIdsByRelationApi(params) {
  return requestClient.get('/upms/user/relation', { params });
}
export async function saveUserRelationApi(data) {
  return requestClient.post('/upms/user/relation/save', data);
}
