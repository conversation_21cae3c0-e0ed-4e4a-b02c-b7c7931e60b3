import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface PositionInfo {
  // 编码, 可选
  code?: string;
  // 名称, 可选
  name?: string;
  // 描述, 可选
  description?: string;
  // 组织Id列表, 可选
  organizeId?: string;
  // 排序, 可选
  sortCode?: number;
  // 是否启用, 可选
  enabled?: number;
  // 扩展属性, 可选
  propertyJson?: string;
}

export function getPositionPageListApi(params: PageListParams) {
  return requestClient.get<PositionInfo[]>('/upms/post/page', { params });
}
export function addPositionApi(data: PositionInfo) {
  return requestClient.post('/upms/post/add', data);
}
export function editPositionApi(data: PositionInfo) {
  return requestClient.post('/upms/post/edit', data);
}
export function deletePositionApi(id: string) {
  return requestClient.post('/upms/post/delete', {}, { params: { id } });
}
export function getPositionListByOrgIdsApi(orgIds: string[]) {
  return requestClient.post('/upms/post/list_by_org_ids', orgIds);
}
