import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'hugeicons:ai-chip',
      title: $t('page.ai.title'),
    },
    name: '<PERSON>',
    path: '/ai',
    children: [
      {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        path: '/ai/model',
        component: () => import('#/views/ai/model/index.vue'),
        meta: {
          icon: 'carbon:model-alt',
          title: $t('page.ai.model'),
        },
      },
      {
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        path: '/ai/secret-key',
        component: () => import('#/views/ai/secret-key/index.vue'),
        meta: {
          icon: 'solar:key-linear',
          title: $t('page.ai.secretKey'),
        },
      },
    ],
  },
];

export default routes;
