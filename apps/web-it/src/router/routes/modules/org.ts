import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'octicon:organization-24',
      title: $t('page.org.title'),
    },
    name: 'Org',
    path: '/org',
    children: [
      {
        name: 'OrgManage',
        path: '/org/manage',
        component: () => import('#/views/org/manage/index.vue'),
        meta: {
          icon: 'eos-icons:organization',
          title: $t('page.org.manage'),
        },
      },
      {
        name: 'OrgMRole',
        path: '/org/role',
        component: () => import('#/views/org/role/index.vue'),
        meta: {
          icon: 'oui:app-users-roles',
          title: $t('page.org.role'),
        },
      },
      {
        name: 'OrgPosition',
        path: '/org/position',
        component: () => import('#/views/org/position/index.vue'),
        meta: {
          icon: 'material-symbols:shelf-position-outline',
          title: $t('page.org.position'),
        },
      },
      {
        name: 'OrgGroup',
        path: '/org/group',
        component: () => import('#/views/org/group/index.vue'),
        meta: {
          icon: 'mingcute:group-3-line',
          title: $t('page.org.group'),
        },
      },
      {
        name: 'UserManage',
        path: '/org/user',
        component: () => import('#/views/org/user/index.vue'),
        meta: {
          icon: 'lucide:user',
          title: $t('page.org.user'),
        },
      },
      {
        name: 'TieredAuth',
        path: '/org/tiered-auth',
        component: () => import('#/views/org/tiered-auth/index.vue'),
        meta: {
          icon: 'fluent-mdl2:passive-authentication',
          title: 'page.org.tieredAuth',
        },
      },
    ],
  },
];

export default routes;
