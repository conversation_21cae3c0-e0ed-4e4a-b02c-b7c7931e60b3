<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { OrderInfo } from '#/api';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Col, DatePicker, Form, FormItem, Input, InputNumber, message, Row, Select, Textarea } from 'ant-design-vue';

import { addPurchaseListApi, editPurchaseListApi, infoPurchaseListApi } from '#/api';

import ProductInfo from './components/product-info.vue';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();
// 根据接口定义初始化信息
const defaultForm: Partial<OrderInfo> = {
  projectId: 123,
  supplierId: 123,
  operationManagerId: 123,
  purchaseOrderCode: '',
  purchaseOrderName: '',
  supplierName: '',
  executorCompanyName: '',
  projectName: '',
  projectCode: '',
  businessStructure: '',
  projectModel: '',
  salesOrderId: '',
  salesOrderCode: '',
  businessDate: '',
  estimatedEndDate: '',
  prepaymentRatio: '',
  prepaymentAmount: '',
  remarks: '',
  businessManagerName: '',
  operationManagerName: '',
  purchaseOrderItemRequests: [],
};

const orderInfo = ref<Partial<OrderInfo>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
// 修改4：更新验证规则以匹配接口字段
const rules: Record<string, Rule[]> = {
  purchaseOrderCode: [{ required: true, message: '请输入采购订单编号', trigger: 'blur' }],
  purchaseOrderName: [{ required: false, message: '请输入采购订单名称', trigger: 'blur' }],
  projectName: [{ required: false, message: '请选择所属项目名称', trigger: 'change' }],
  projectCode: [{ required: false, message: '请输入所属项目编号', trigger: 'blur' }],
  businessStructure: [{ required: false, message: '请输入业务结构', trigger: 'blur' }],
  projectModel: [{ required: false, message: '请输入项目模式', trigger: 'blur' }],
  salesOrderId: [{ required: false, message: '请选择关联销售订单', trigger: 'change' }],
  salesOrderCode: [{ required: false, message: '请输入销售订单编号', trigger: 'blur' }],
  executorCompanyName: [{ required: false, message: '请输入贸易执行企业名称', trigger: 'blur' }],
  supplierName: [{ required: false, message: '请选择上游企业/供应商', trigger: 'change' }],
  businessManagerName: [{ required: false, message: '请输入业务负责人名称', trigger: 'blur' }],
  operationManagerName: [{ required: false, message: '请输入运营负责人名称', trigger: 'blur' }],
  businessDate: [{ required: false, message: '请选择业务日期', trigger: 'change' }],
  estimatedEndDate: [{ required: false, message: '请选择预计结束日期', trigger: 'change' }],
  prepaymentRatio: [{ required: false, message: '请输入预付款比例', trigger: 'blur' }],
  prepaymentAmount: [{ required: false, message: '请输入预付款金额', trigger: 'blur' }],
  remarks: [{ required: false, message: '请输入备注', trigger: 'blur' }],
};
const title = computed(() => {
  return orderInfo.value.id ? '编辑采购订单' : '新增采购订单';
});
const productGridRef = ref();

const init = async (data: OrderInfo) => {
  if (data.id) {
    orderInfo.value = await infoPurchaseListApi({ id: data.id as number });
    productGridRef.value.setProductData(orderInfo.value.purchaseOrderItemRequests);
  }
};

const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  orderInfo.value.purchaseOrderItemRequests = productGridRef.value?.getProductData() || [];
  changeOkLoading(true);
  let api = addPurchaseListApi;
  if (orderInfo.value.id) {
    api = editPurchaseListApi;
  }
  try {
    const res = await api(orderInfo.value as OrderInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="orderInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基础信息 -->
      <BasicCaption content="基础信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="采购订单编号" name="purchaseOrderCode">
            <Input v-model:value="orderInfo.purchaseOrderCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="采购订单名称" name="purchaseOrderName">
            <Input v-model:value="orderInfo.purchaseOrderName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="所属项目名称" name="projectName">
            <Select v-model:value="orderInfo.projectName" :options="getDictList('industry')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="所属项目编号" name="projectCode">
            <Input v-model:value="orderInfo.projectCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务结构" name="businessStructure">
            <Input v-model:value="orderInfo.businessStructure" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目模式" name="projectModel">
            <Input v-model:value="orderInfo.projectModel" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="关联销售订单" name="salesOrderId">
            <Select v-model:value="orderInfo.salesOrderId" :options="getDictList('industry')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="销售订单编号" name="salesOrderCode">
            <Input v-model:value="orderInfo.salesOrderCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="贸易执行企业" name="executorCompanyName">
            <Input v-model:value="orderInfo.executorCompanyName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="上游企业" name="supplierName">
            <Select v-model:value="orderInfo.supplierName" :options="getDictList('industry')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务负责人" name="businessManagerName">
            <Input v-model:value="orderInfo.businessManagerName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="运营负责人" name="operationManagerName">
            <Input v-model:value="orderInfo.operationManagerName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务日期" name="businessDate">
            <DatePicker v-model:value="orderInfo.businessDate" value-format="YYYY-MM-DD" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="预计结束日期" name="estimatedEndDate">
            <DatePicker v-model:value="orderInfo.estimatedEndDate" value-format="YYYY-MM-DD" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="预付款比例(%)" name="prepaymentRatio">
            <InputNumber v-model:value="orderInfo.prepaymentRatio" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem name="prepaymentAmount">
            <template #label>
              预付款金额
              <VbenTooltip title="默认预付款金额=采购订单总金额*预付款比例">
                <VbenIcon icon="ant-design:question-circle-outlined" class="mr-1 cursor-pointer text-base" />
              </VbenTooltip>
            </template>
            <InputNumber v-model:value="orderInfo.prepaymentAmount" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="orderInfo.remarks" :rows="4" />
          </FormItem>
        </Col>
      </Row>

      <BasicCaption content="商品信息" />
      <ProductInfo ref="productGridRef" />
    </Form>
  </BasicPopup>
</template>
