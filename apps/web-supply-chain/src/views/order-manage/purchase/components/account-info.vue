<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { useDictStore } from '@vben/stores';

import { Button, Input, message, Select, Switch } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProductPageListApi } from '#/api';

const { getDictList } = useDictStore();

const accountList = ref<AccountInfo[]>([]);

// 新增初始化方法
const setAccountData = (data: AccountInfo[]) => {
  accountList.value = data;
  if (gridApi.grid) {
    gridApi.grid.reloadData(accountList.value);
  }
};

// 表格配置
const gridOptions: VxeTableGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  editRules: {
    bankName: [{ required: true, message: '请输入账户名称' }],
    depositBank: [{ required: true, message: '请输入开户行' }],
    bankAccount: [{ required: true, message: '请输入银行账号' }],
  },
  columns: [
    { type: 'checkbox', width: '40px', fixed: 'left' },
    {
      field: 'status',
      title: '商品分类',
      slots: { default: 'edit_status' },
      minWidth: '100px',
    },
    { field: 'bankName', title: '商品名称', editRender: {}, slots: { edit: 'edit_bank_name' }, minWidth: '160px' },
    {
      field: 'depositBank',
      title: 'SKU名称',
      editRender: {},
      slots: { edit: 'edit_deposit_bank' },
      minWidth: '160px',
    },
    {
      field: 'bankAccount',
      title: '商品编码',
      editRender: {},
      slots: { edit: 'edit_bank_account' },
      minWidth: '160px',
    },
    {
      field: 'depositBankNumber',
      title: '计量单位',
      editRender: {},
      slots: { edit: 'edit_deposit_bank_number' },
      minWidth: '160px',
    },
    {
      field: 'bankType',
      title: '规格属性',
      editRender: {},
      slots: { edit: 'edit_bank_type' },
      minWidth: '160px',
    },
    {
      field: 'receiptType',
      title: '商品品牌',
      editRender: {},
      slots: { edit: 'edit_receipt_type' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '生产厂家',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '采购数量',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '含税单价',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '税率(%)',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '含税金额',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '税额',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '不含税金额',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '不含税单价',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '本单商品行号',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '主订单商品行号',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
    {
      field: 'description',
      title: '备注',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
    },
  ],
  data: accountList.value,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const res = await getProductPageListApi({
          current: page.currentPage,
          size: page.pageSize,
        });
        if (res?.records) {
          gridApi.grid.reloadData(res.records);
        }
        return res;
      },
    },
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 增加账户行
const addAccount = async () => {
  const record = {};
  const $grid = gridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};

// 删除账户行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  getAccountData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const { visibleData } = gridApi.grid.getTableData();
      return visibleData;
    } else {
      return [];
    }
  },
  setAccountData,
});
</script>

<template>
  <div>
    <Grid>
      <template #toolbarTools>
        <!-- <Button class="mr-2" type="primary" @click="addAccount">增行</Button> -->
        <Button class="mr-2" type="primary" @click="removeAccount">模板下载</Button>
        <Button class="mr-2" type="primary" @click="removeAccount">导入商品</Button>
        <Button class="mr-2" type="primary" @click="removeAccount">批量设置</Button>
        <Button class="mr-2" type="primary" @click="removeAccount">重置</Button>
        <Button class="mr-2" danger @click="removeAccount">删行</Button>
      </template>
      <template #edit_status="{ row }">
        <Switch v-model:checked="row.status" :checked-value="1" :un-checked-value="0" />
      </template>
      <template #edit_bank_name="{ row }">
        <Input v-model:value="row.bankName" placeholder="请输入账户名称" />
      </template>
      <template #edit_deposit_bank="{ row }">
        <Input v-model:value="row.depositBank" placeholder="请输入开户行" />
      </template>
      <template #edit_bank_account="{ row }">
        <Input v-model:value="row.bankAccount" placeholder="请输入银行账号" />
      </template>
      <template #edit_deposit_bank_number="{ row }">
        <Input v-model:value="row.depositBankNumber" placeholder="请输入开户行联号" />
      </template>
      <template #edit_bank_type="{ row }">
        <Select v-model:value="row.bankType" :options="getDictList('bankType')" class="w-full" />
      </template>
      <template #edit_receipt_type="{ row }">
        <Select v-model:value="row.receiptType" :options="getDictList('receiptType')" class="w-full" />
      </template>
      <template #edit_is_default="{ row }">
        <Input v-model:value="row.description" placeholder="请输入开户行联号" />
      </template>
    </Grid>
  </div>
</template>

<style scoped></style>
