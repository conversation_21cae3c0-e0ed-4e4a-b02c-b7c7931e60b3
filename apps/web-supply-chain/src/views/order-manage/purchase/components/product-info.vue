<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ProductInfo } from '#/api';

import { ref } from 'vue';

import { Button, Input, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

const accountList = ref<ProductInfo[]>([]);

// 新增初始化方法
const setProductData = (data: ProductInfo[]) => {
  accountList.value = data;
  if (gridApi.grid) {
    gridApi.grid.reloadData(accountList.value);
  }
};

// 表格配置
const gridOptions: VxeTableGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  editRules: {
    bankName: [{ required: true, message: '请输入账户名称' }],
    depositBank: [{ required: true, message: '请输入开户行' }],
    bankAccount: [{ required: true, message: '请输入银行账号' }],
  },
  columns: [
    { type: 'checkbox', width: '40px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      slots: { default: 'edit_productName' },
      minWidth: '100px',
    },
    {
      field: 'spuName',
      title: '商品别名',
      slots: { default: 'edit_spuName' },
      minWidth: '160px',
    },
    {
      field: 'skuName',
      title: '规格型号',
      editRender: {},
      slots: { default: 'edit_skuName' },
      minWidth: '160px',
    },
    {
      field: 'spuCode',
      title: '商品编码',
      editRender: {},
      slots: { default: 'edit_spuCode' },
      minWidth: '160px',
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      editRender: {},
      slots: { default: 'edit_measureUnit' },
      minWidth: '160px',
    },
    {
      field: 'brandName',
      title: '商品品牌',
      slots: { default: 'edit_brandName' },
      minWidth: '160px',
    },
    {
      field: 'manufacturerName',
      title: '生产厂家',
      slots: { default: 'edit_manufacturerName' },
      minWidth: '160px',
    },
    {
      field: 'quantity',
      title: '采购数量',
      slots: { default: 'edit_quantity' },
      minWidth: '160px',
    },
    {
      field: 'priceWithTax',
      title: '含税单价',
      slots: { default: 'edit_priceWithTax' },
      minWidth: '160px',
    },
    {
      field: 'taxRate',
      title: '税率(%)',
      slots: { default: 'edit_taxRate' },
      minWidth: '160px',
    },
    {
      field: 'amountWithTax',
      title: '含税金额',
      slots: { default: 'edit_amountWithTax' },
      minWidth: '160px',
    },
    {
      field: 'taxAmount',
      title: '税额',
      slots: { default: 'edit_taxAmount' },
      minWidth: '160px',
    },
    {
      field: 'amountWithoutTax',
      title: '不含税金额',
      slots: { default: 'edit_amountWithoutTax' },
      minWidth: '160px',
    },
    {
      field: 'priceWithoutTax',
      title: '不含税单价',
      slots: { default: 'edit_priceWithoutTax' },
      minWidth: '160px',
    },
    {
      field: 'purchaseOrderCode',
      title: '采购订单行号',
      slots: { default: 'edit_purchaseOrderCode' },
      minWidth: '160px',
    },
    {
      field: 'remarks',
      title: '备注',
      slots: { default: 'edit_remarks' },
      minWidth: '160px',
    },
  ],
  data: accountList.value,
  // proxyConfig: {
  //   ajax: {
  //     query: async ({ page }) => {
  //       const res = await getPurchaseListApi({
  //         current: page.currentPage,
  //         size: page.pageSize,
  //       });
  //       if (res?.records) {
  //          gridApi.grid.reloadData(res.records);
  //       }
  //     },
  //   },
  // },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 增加账户行
const addAccount = async () => {
  const record = {};
  const $grid = gridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};

// 删除账户行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  getProductData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const { visibleData } = gridApi.grid.getTableData();

      // 插入写死字段
      const processedData = visibleData.map((item) => ({
        ...item,
        purchaseOrderId: 0,
      }));

      return processedData;
      //return visibleData;
    } else {
      return [];
    }
  },
  setProductData,
});
</script>

<template>
  <div>
    <Grid>
      <template #toolbarTools>
        <Button class="mr-2" type="primary" @click="removeAccount">模板下载</Button>
        <Button class="mr-2" type="primary" @click="removeAccount">导入商品</Button>
        <Button class="mr-2" type="primary" @click="removeAccount">批量设置</Button>
        <Button class="mr-2" type="primary" @click="removeAccount">重置</Button>
        <Button class="mr-2" type="primary" @click="addAccount">增行</Button>
        <Button class="mr-2" danger @click="removeAccount">删行</Button>
      </template>
      <template #edit_productName="{ row }">
        <Input v-model:value="row.productName" placeholder="请输入商品名称" />
      </template>
      <template #edit_spuName="{ row }">
        <Input v-model:value="row.spuName" placeholder="请输入商品别名" />
      </template>
      <template #edit_skuName="{ row }">
        <Input v-model:value="row.skuName" placeholder="请输入规格型号" />
      </template>
      <template #edit_spuCode="{ row }">
        <Input v-model:value="row.spuCode" placeholder="请输入商品编码" />
      </template>
      <template #edit_measureUnit="{ row }">
        <Input v-model:value="row.measureUnit" placeholder="请输入计量单位" />
      </template>
      <template #edit_brandName="{ row }">
        <Input v-model:value="row.brandName" placeholder="请输入商品品牌" />
      </template>
      <template #edit_manufacturerName="{ row }">
        <Input v-model:value="row.manufacturerName" placeholder="请输入生产厂家" />
      </template>
      <template #edit_quantity="{ row }">
        <InputNumber v-model:value="row.quantity" placeholder="请输入采购数量" />
      </template>
      <template #edit_priceWithTax="{ row }">
        <InputNumber v-model:value="row.priceWithTax" placeholder="请输入含税单价" />
      </template>
      <template #edit_taxRate="{ row }">
        <InputNumber v-model:value="row.taxRate" placeholder="请输入税率" />
      </template>
      <template #edit_amountWithTax="{ row }">
        <InputNumber v-model:value="row.amountWithTax" placeholder="请输入含税金额" />
      </template>
      <template #edit_taxAmount="{ row }">
        <InputNumber v-model:value="row.taxAmount" placeholder="请输入税额" />
      </template>
      <template #edit_amountWithoutTax="{ row }">
        <InputNumber v-model:value="row.amountWithoutTax" placeholder="请输入不含税金额" />
      </template>
      <template #edit_priceWithoutTax="{ row }">
        <InputNumber v-model:value="row.priceWithoutTax" placeholder="请输入不含税单价" />
      </template>
      <template #edit_purchaseOrderCode="{ row }">
        <Input v-model:value="row.purchaseOrderCode" placeholder="请输入采购订单行号" />
      </template>
      <template #edit_remarks="{ row }">
        <Input v-model:value="row.remarks" placeholder="请输入备注" />
      </template>
    </Grid>
  </div>
</template>

<style scoped></style>
