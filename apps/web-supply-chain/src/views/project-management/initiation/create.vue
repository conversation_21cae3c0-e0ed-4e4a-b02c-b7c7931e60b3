<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { InitiationBaseInfo, InitiationGoodsInfo } from '#/api';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import {
  Button,
  Col,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Radio,
  RadioGroup,
  Row,
  Select, Switch,
  Textarea,
} from 'ant-design-vue';

import { addProductApi, editProductApi, infoProductApi } from '#/api';

const emit = defineEmits(['register', 'ok']);
const { getDictList } = useDictStore();
// 根据接口定义初始化产品信息
const defaultForm: Partial<InitiationBaseInfo> = {
  projectName: '',
  businessStructure: undefined,
  projectModel: undefined,
  executorCompanyName: '',
  businessManagerName: undefined,
  supplierCompanyName: undefined,
  purchaserCompanyName: undefined,
  creditCompanyName: undefined,
  isDeposit: undefined,
  isGoodsControlMode: undefined,
  serviceFeeRate: '',
  paymentTermDays: '',
  expectedProjectScale: '',
  businessDate: undefined,
  estimatedEndDate: undefined,
  purchaseMode: undefined,
  projectCode: '',
  remarks: '',
};

const goodsInfoForm: Partial<InitiationGoodsInfo> = {
  id: 0,
  createBy: 0,
  createTime: "2025-07-04T09:50:09.494Z",
  updateBy: 0,
  updateTime: "2025-07-04T09:50:09.494Z",
  deleteFlag: 0,
  projectId: 0,
  productName: "string",
  productCode: "string",
  measureUnit: "string",
  specifications: "string",
  brandName: "string",
  manufacturerName: "string",
  quantity: 0,
  taxRate: 0,
  purchasePriceWithTax: 0,
  purchaseAmountWithTax: 0,
  purchasePriceWithoutTax: 0,
  purchaseAmountWithoutTax: 0,
  salePriceWithTax: 0,
  saleAmountWithTax: 0,
  salePriceWithoutTax: 0,
  saleAmountWithoutTax: 0,
  remarks: "string"
}

const productInfo = ref<Partial<InitiationBaseInfo>>(defaultsDeep(defaultForm));
const goodsInfo = ref<Partial<InitiationGoodsInfo>>(defaultsDeep(goodsInfoForm));
const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  businessStructure: [{ required: true, message: '请选择业务结构', trigger: 'change' }],
  projectModel: [{ required: true, message: '请选择项目模式', trigger: 'change' }],
  executorCompanyName: [{ required: true, message: '请输入贸易执行企业', trigger: 'change' }],
  businessManagerName: [{ required: true, message: '请选择业务负责人', trigger: 'change' }],
  supplierCompanyName: [{ required: true, message: '请选择上游企业', trigger: 'change' }],
  purchaserCompanyName: [{ required: true, message: '请选择下游企业', trigger: 'change' }],
  creditCompanyName: [{ required: true, message: '请选择终端企业', trigger: 'change' }],
  projectAddressSuf: [{ required: true, message: '请输入项目地点', trigger: 'change' }],
  isDeposit: [{ required: true, message: '请选择是否有保证金', trigger: 'change' }],
  purchaseMode: [{ required: true, message: '请选择采购模式', trigger: 'change' }],
  isGoodsControlMode: [{ required: true, message: '请选择是否是控货模式', trigger: 'change' }],
  serviceFeeRate: [{ required: true, message: '请输入服务费率', trigger: 'change' }],
  paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'change' }],
};
const title = computed(() => {
  return productInfo.value.id ? '编辑产品' : '新增产品';
});
const init = async (data: InitiationBaseInfo) => {
  if (data.id) {
    productInfo.value = await infoProductApi({ id: data.id as string });
  }
};
const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  changeOkLoading(true);
  let api = addProductApi;
  if (productInfo.value.id) {
    api = editProductApi;
  }
  try {
    const res = await api(productInfo.value as InitiationBaseInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const labelCol = { style: { width: '150px' } };

const accountList = ref<AccountInfo[]>([]);

// 新增初始化方法
const setAccountData = (data: AccountInfo[]) => {
  accountList.value = data;
  if (gridApi.grid) {
    gridApi.grid.reloadData(accountList.value);
  }
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  editRules: {
    bankName: [{ required: true, message: '请输入账户名称' }],
    depositBank: [{ required: true, message: '请输入开户行' }],
    bankAccount: [{ required: true, message: '请输入银行账号' }],
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'bankName', title: '商品分类', editRender: {}, slots: { edit: 'edit_bank_name' }, minWidth: '160px' },
    {
      field: 'depositBank',
      title: '商品名称',
      editRender: {},
      slots: { edit: 'edit_deposit_bank' },
      minWidth: '160px',
    },
    {
      field: 'bankAccount',
      title: 'SKU名称',
      editRender: {},
      slots: { edit: 'edit_bank_account' },
      minWidth: '160px',
    },
    {
      field: 'depositBankNumber',
      title: '商品编码',
      editRender: {},
      slots: { edit: 'edit_deposit_bank_number' },
      minWidth: '160px',
    },
    {
      field: 'bankType',
      title: '计量单位',
      editRender: {},
      slots: { edit: 'edit_bank_type' },
      minWidth: '160px',
    },
    {
      field: 'receiptType',
      title: '规格属性',
      editRender: {},
      slots: { edit: 'edit_receipt_type' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '商品品牌',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '生产厂家',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '交易数量',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '含税采购单价',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '含税销售单价',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '税率(%)',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '含税采购金额',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '不含税采购金额',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '不含税采购单价',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '含税销售金额',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '不含税销售金额',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '不含税销售单价',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
    {
      field: 'isDefault',
      title: '备注',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
  ],
  data: accountList.value,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 增加账户行
const addAccount = async () => {
  const record = {};
  const $grid = gridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};

// 删除账户行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  getAccountData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const { visibleData } = gridApi.grid.getTableData();
      return visibleData;
    } else {
      return [];
    }
  },
  setAccountData,
});

</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
        ref="formRef"
        :colon="false"
        :model="productInfo"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="{ span: 20 }"
        class="px-8"
    >
      <!-- 基础信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="项目编号" name="projectCode">
            <Input v-model:value="productInfo.projectCode" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目名称" name="projectName">
            <Input v-model:value="productInfo.projectName"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务结构" name="businessStructure">
            <Select v-model:value="productInfo.businessStructure" :options="getDictList('businessStructure')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目模式" name="projectModel">
            <Select v-model:value="productInfo.projectModel" :options="getDictList('projectModel')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="贸易执行企业" name="executorCompanyName">
            <Select v-model:value="productInfo.executorCompanyName" :options="getDictList('executorCompanyName')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务负责人" name="businessManagerName">
            <Select v-model:value="productInfo.businessManagerName" :options="getDictList('businessManagerName')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="上游企业" name="supplierCompanyName">
            <Select v-model:value="productInfo.supplierCompanyName" :options="getDictList('supplierCompanyName')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="下游企业" name="purchaserCompanyName">
            <Select v-model:value="productInfo.purchaserCompanyName" :options="getDictList('purchaserCompanyName')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="终端企业" name="creditCompanyName">
            <Select v-model:value="productInfo.creditCompanyName" :options="getDictList('creditCompanyName')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目地点" name="projectAddress">
            <Row>
              <Col v-bind="colSpan">
                <Select v-model:value="productInfo.projectAddress" :options="getDictList('projectAddress')" />
              </Col>
              <Col v-bind="colSpan">
                <Input v-model:value="productInfo.projectAddress" />
              </Col>
            </Row>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否有保证金" name="isDeposit">
            <Select v-model:value="productInfo.isDeposit" :options="getDictList('isDeposit')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="采购模式" name="purchaseMode">
            <Select v-model:value="productInfo.purchaseMode" :options="getDictList('purchaseMode')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否是控货模式" name="isGoodsControlMode">
            <Select v-model:value="productInfo.isGoodsControlMode" :options="getDictList('isGoodsControlMode')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="服务费率（年%）" name="serviceFeeRate">
            <Input v-model:value="productInfo.serviceFeeRate" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="账期（天)" name="paymentTermDays">
            <Input v-model:value="productInfo.paymentTermDays"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目预期规模(元)" name="expectedProjectScale">
            <Input v-model:value="productInfo.expectedProjectScale"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务日期" name="businessDate">
            <ADatePicker v-model:value="productInfo.businessDate"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="预计结束日期" name="estimatedEndDate">
            <ADatePicker v-model:value="productInfo.estimatedEndDate"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="productInfo.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="商品信息" />
      <div>
        <Grid>
          <template #toolbarTools>
            <Button class="mr-2" type="primary" @click="addAccount">增行</Button>
            <Button class="mr-2" danger @click="removeAccount">删行</Button>
          </template>
          <template #edit_status="{ row }">
            <Switch v-model:checked="row.status" :checked-value="1" :un-checked-value="0" />
          </template>
          <template #edit_bank_name="{ row }">
            <Input v-model:value="row.bankName" placeholder="请输入账户名称" />
          </template>
          <template #edit_deposit_bank="{ row }">
            <Input v-model:value="row.depositBank" placeholder="请输入开户行" />
          </template>
          <template #edit_bank_account="{ row }">
            <Input v-model:value="row.bankAccount" placeholder="请输入银行账号" />
          </template>
          <template #edit_deposit_bank_number="{ row }">
            <Input v-model:value="row.depositBankNumber" placeholder="请输入开户行联号" />
          </template>
          <template #edit_bank_type="{ row }">
            <Select v-model:value="row.bankType" :options="getDictList('bankType')" class="w-full" />
          </template>
          <template #edit_receipt_type="{ row }">
            <Select v-model:value="row.receiptType" :options="getDictList('receiptType')" class="w-full" />
          </template>
          <template #edit_is_default="{ row }">
            <Switch v-model:checked="row.isDefault" :checked-value="1" :un-checked-value="0" />
          </template>
        </Grid>
      </div>
      <!-- 高级设置 -->
      <BasicCaption content="增信措施" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="还款方式" name="repaymentMethod">
            <Input v-model:value="goodsInfo.repaymentMethod" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="还款期数" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="repaymentPeriodsMin" class="mr-2 w-1/2">
                <InputNumber v-model:value="goodsInfo.repaymentPeriodsMin" :controls="false" class="w-full" />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="repaymentPeriodsMax" class="ml-2 w-1/2">
                <InputNumber v-model:value="goodsInfo.repaymentPeriodsMax" :controls="false" class="w-full" />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="年化利率（%）" required class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="annualizedInterestRateMin" class="mr-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.annualizedInterestRateMin"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="annualizedInterestRateMax" class="ml-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.annualizedInterestRateMax"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="融资比例（%）" required class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="financingRatioMin" class="mr-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.financingRatioMin"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="financingRatioMax" class="ml-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.financingRatioMax"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="手续费率（%）" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="serviceFeeFixedMin" class="mr-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.serviceFeeFixedMin"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="serviceFeeFixedMax" class="ml-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.serviceFeeFixedMax"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="固定手续费（元）" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="serviceFeeMin" class="mr-2 w-1/2">
                <InputNumber v-model:value="goodsInfo.serviceFeeMin" :controls="false" class="w-full" />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="serviceFeeMax" class="ml-2 w-1/2">
                <InputNumber v-model:value="goodsInfo.serviceFeeMax" :controls="false" class="w-full" />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否有宽限期" name="isGracePeriod">
            <RadioGroup v-model:value="goodsInfo.isGracePeriod">
              <Radio v-for="item in getDictList('isGracePeriod')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="宽限期类型" name="gracePeriodType">
            <RadioGroup v-model:value="goodsInfo.gracePeriodType">
              <Radio v-for="item in getDictList('gracePeriodType')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="融资宽限期（天）" required class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="gracePeriodMin" class="mr-2 w-1/2">
                <InputNumber v-model:value="goodsInfo.gracePeriodMin" :controls="false" class="w-full" />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="gracePeriodMax" class="ml-2 w-1/2">
                <InputNumber v-model:value="goodsInfo.gracePeriodMax" :controls="false" class="w-full" />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="宽限期类型" name="isInterestAdd">
            <RadioGroup v-model:value="goodsInfo.isInterestAdd">
              <Radio v-for="item in getDictList('isInterestAdd')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="加息天数（天）" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="interestAddDaysMin" class="mr-2 w-1/2">
                <InputNumber v-model:value="goodsInfo.interestAddDaysMin" :controls="false" class="w-full" />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="interestAddDaysMax" class="ml-2 w-1/2">
                <InputNumber v-model:value="goodsInfo.interestAddDaysMax" :controls="false" class="w-full" />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="加息方式" name="interestAddMethod">
            <RadioGroup v-model:value="goodsInfo.interestAddMethod">
              <Radio v-for="item in getDictList('interestAddMethod')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="固定加息率（%）" required class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="interestAddRateMin" class="mr-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.interestAddRateMin"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="interestAddRateMax" class="ml-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.interestAddRateMax"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="罚息类型" name="penaltyInterestType">
            <RadioGroup v-model:value="goodsInfo.penaltyInterestType">
              <Radio v-for="item in getDictList('penaltyInterestType')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="风险系数" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="riskCoefficientMin" class="mr-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.riskCoefficientMin"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="riskCoefficientMax" class="ml-2 w-1/2">
                <InputNumber
                    v-model:value="goodsInfo.riskCoefficientMax"
                    :controls="false"
                    :precision="2"
                    class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
