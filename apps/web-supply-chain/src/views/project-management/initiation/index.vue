<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';
import { VbenIcon } from '@vben-core/shadcn-ui';

// import { delProductApi, getProductPageListApi, FactoringProduct } from '#/api';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import Create from './create.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目立项编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目立项名称',
    },
    {
      component: 'Select',
      fieldName: 'businessStructure',
      label: '业务结构',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'supplierCompanyName',
      label: '上游企业',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'purchaserCompanyName',
      label: '下游企业',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'executorCompanyName',
      label: '贸易执行企业',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'upStatus',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Input',
      fieldName: 'businessManagerName',
      label: '业务负责人',
    },
    {
      component: 'RangePicker',
      fieldName: 'productName',
      label: '业务日期',
    },
  ],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    { field: 'businessStructure', title: '业务结构' },
    { field: 'projectModel', title: '项目模式' },
    { field: 'supplierCompanyName', title: '上游企业' },
    { field: 'purchaserCompanyName', title: '下游企业' },
    { field: 'executorCompanyName', title: '贸易执行企业' },
    { field: 'projectCode', title: '业务状态' }, // todo
    { field: 'approvalStatus', title: '审批状态' },
    { field: 'businessManagerName', title: '业务负责人' },
    { field: 'businessDate', title: '业务日期' },
    { field: 'createTime', title: '创建时间' },
    { field: 'createBy', title: '创建人' },
    { field: 'createDeptName', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getProductPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: FactoringProduct) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const del = async (row: FactoringProduct) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      // await delProductApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="user" @click="edit(row)"> 变更 </MenuItem>
                <MenuItem key="view"> 上架 </MenuItem>
                <MenuItem key="view"> 下架 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
