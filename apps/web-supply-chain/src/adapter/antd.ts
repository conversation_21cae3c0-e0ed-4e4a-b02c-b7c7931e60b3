import type { App, Plugin } from 'vue';

import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  TreeSelect,
  Typography,
  DatePicker
} from 'ant-design-vue';

// 需要注册的组件列表
const components: Plugin[] = [
  Button,
  Form,
  TreeSelect,
  Input,
  InputNumber,
  Space,
  Typography,
  Row,
  Col,
  Checkbox,
  Select,
  DatePicker
];

export function setupAntd(app: App) {
  components.forEach((component) => {
    app.use(component);
  });
}
