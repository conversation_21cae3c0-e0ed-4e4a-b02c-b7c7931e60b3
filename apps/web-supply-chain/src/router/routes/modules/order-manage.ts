import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: '',
      order: -1,
      title: $t('page.order-manage.title'),
    },
    name: 'order-manage',
    path: '/order-manage',
    children: [
      {
        name: 'purchase',
        path: '/order-manage/purchase',
        component: () => import('#/views/order-manage/purchase/index.vue'),
        meta: {
          icon: '',
          title: $t('page.order-manage.purchase'),
        },
      },
    ],
  },
];

export default routes;
