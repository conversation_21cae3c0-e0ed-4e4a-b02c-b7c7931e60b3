import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface InitiationBaseInfo extends BaseDataParams {
  // 当前页
  current: string;
  // 每页的数量
  size: string;
  // 正排序规则
  ascs: string;
  // 倒排序规则
  descs: string;
  // 版本号
  version: string;
  // 项目编码
  projectCode: string;
  // 项目名称
  projectName: string;
  // 上游企业ID
  supplierCompanyId: string;
  // 上游企业名称
  supplierCompanyName: string;
  // 下游企业ID
  purchaserCompanyId: string;
  // 下游企业名称
  purchaserCompanyName: string;
  // 终端企业ID
  creditCompanyId: string;
  // 终端企业名称
  creditCompanyName: string;
  // 贸易执行企业ID
  executorCompanyId: string;
  // 贸易执行企业名称
  executorCompanyName: string;
  // 业务负责人ID
  businessManagerId: string;
  // 业务负责人名称
  businessManagerName: string;
  // 业务结构(先采后销, 先销后采)
  businessStructure: string;
  // 项目模式(建材模式, 产业模式)
  projectModel: string;
  // 采购模式(预付款, 货到付款)
  purchaseMode: string;
  // 是否货权管控模式 (1:是, 0:否)
  isGoodsControlMode: string;
  // 账期天数（天)
  paymentTermDays: string;
  // 业务日期
  businessDate: string;
  // 预计结束日期
  estimatedEndDate: string;
  // 预期项目规模(元)
  expectedProjectScale: string;
  // 服务费率（年/%)
  serviceFeeRate: string;
  // 项目地点
  projectAddress: string;
  // 项目备注
  remarks: string;
  // 是否有保证金 (1:是, 0:否)
  isDeposit: string;
  // 抵押信息描述
  mortgageInfoDesc: string;
  // 质押信息描述
  pledgeInfoDesc: string;
  // 担保信息描述
  guaranteeInfoDesc: string;
  // 决策材料描述
  decisionMaterialsDesc: string;
  // 操作状态
  status: string;
  // 审批状态
  approvalStatus: string;
  // 主键
  id: string;
  // 创建人
  createBy: string;
  // 创建时间
  createTime: string;
  // 更新人
  updateBy: string;
  // 更新时间
  updateTime: string;
  // 逻辑删除
  deleteFlag: string;
}

export interface InitiationGoodsInfo extends BaseDataParams {
  // 主键
  id: number;
  // 创建人
  createBy: number;
  // 创建时间
  createTime: string;
  // 更新人ID
  updateBy: number;
  // 更新时间
  updateTime: string;
  // 删除标志 (0:未删除, 1:已删除)
  deleteFlag: number;
  // 项目ID
  projectId: number;
  // 产品名称
  productName: string;
  // 产品编码
  productCode: string;
  // 计量单位
  measureUnit: string;
  // 规格型号
  specifications: string;
  // 品牌名称
  brandName: string;
  // 生产厂家名称
  manufacturerName: string;
  // 数量
  quantity: number;
  // 税率 (%)
  taxRate: number;
  // 含税采购单价
  purchasePriceWithTax: number;
  // 含税采购金额
  purchaseAmountWithTax: number;
  // 不含税采购单价
  purchasePriceWithoutTax: number;
  // 不含税采购金额
  purchaseAmountWithoutTax: number;
  // 含税销售单价
  salePriceWithTax: number;
  // 含税销售金额
  saleAmountWithTax: number;
  // 不含税销售单价
  salePriceWithoutTax: number;
  // 不含税销售金额
  saleAmountWithoutTax: number;
  // 备注
  remarks: string;
}

export async function getProductPageListApi(params: PageListParams) {
  return requestClient.get<FactoringProduct[]>('/upms/user/page', { params });
}
export async function addProductApi(data: FactoringProduct) {
  return requestClient.post<FactoringProduct>('/upms/user/add', data);
}
export async function editProductApi(data: FactoringProduct) {
  return requestClient.post<FactoringProduct>('/upms/user/edit', data);
}
export async function infoProductApi(data: FactoringProduct) {
  return requestClient.post<FactoringProduct>('/upms/user/info', data);
}
export async function delProductApi(id: string) {
  return requestClient.post('/upms/user/delete', {}, { params: { id } });
}
