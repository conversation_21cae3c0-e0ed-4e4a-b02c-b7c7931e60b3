import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// 产品信息
export interface ProductInfo extends BaseDataParams {
  purchaseOrderId?: number;
  // 状态
  status: string;
  // 商品名称
  productName?: string;
  // 商品别名
  spuName?: string;
  // 规格型号
  skuCode?: string;
  // 商品编码
  spuCode?: string;
  // 计量单位
  measureUnit?: string;
  // 商品品牌
  brandName?: string;
  // 生产厂家
  manufacturerName?: string;
  // 采购数量
  quantity?: string;
  // 含税单价
  priceWithTax?: string;
  // 税率(%)
  taxRate?: string;
  // 含税金额
  amountWithTax?: string;
  // 税额
  taxAmount?: string;
  // 不含税金额
  amountWithoutTax?: string;
  // 不含税单价
  priceWithoutTax?: string;
  // 采购订单行号
  purchaseOrderCode?: string;
  // 备注
  remarks?: string;
}
// 订单信息接口扩展
export interface OrderInfo {
  id?: number;
  projectId?: number;
  supplierId?: number;
  operationManagerId?: number;
  // 采购订单编号
  purchaseOrderCode?: string;
  // 采购订单名称
  purchaseOrderName?: string;
  // 上游企业/供应商名称
  supplierName?: string;
  // 贸易执行企业名称
  executorCompanyName?: string;
  // 所属项目名称
  projectName?: string;
  // 所属项目编号
  projectCode?: string;
  // 业务结构 (SELL_FIRST_BUY_LATER, BUY_FIRST_SELL_LATER)
  businessStructure?: string;
  // 项目模式 (建材模式, 产业模式等)
  projectModel?: string;
  // 关联销售订单ID (用于"先销后采"模式)
  salesOrderId?: string;
  // 关联销售订单编号
  salesOrderCode?: string;
  // 业务日期
  businessDate?: string;
  // 预计结束日期
  estimatedEndDate?: string;
  // 预付款比例(%)
  prepaymentRatio?: string;
  // 预付款金额
  prepaymentAmount?: string;
  // 备注
  remarks?: string;
  // 业务负责人名称
  businessManagerName?: string;
  // 运营负责人名称
  operationManagerName?: string;
  purchaseOrderItemRequests?: ProductInfo[];
}

export async function getPurchaseListApi(params: PageListParams) {
  return requestClient.get<OrderInfo[]>('/scm/order/purchase/list', { params });
}
export async function addPurchaseListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/purchase/save', data);
}
export async function editPurchaseListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/purchase/update', data);
}
export async function infoPurchaseListApi(params: OrderInfo) {
  return requestClient.get<OrderInfo>('/scm/order/purchase/detail', { params });
}
export async function delEnterpriseApi(id: string) {
  return requestClient.post('/upms/user/delete', {}, { params: { id } });
}
