import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        proxy: {
          '/jxct/api/ai': {
            changeOrigin: true,
            // mock代理目标地址
            target: 'http://192.168.0.160',
            ws: true,
          },
          '/jxct/api/upms': {
            changeOrigin: true,
            // mock代理目标地址
            target: 'http://*************',
            ws: true,
          },
          '/jxct/api/scm': {
            changeOrigin: true,
            // mock代理目标地址
            target: 'http://*************',
            ws: true,
          },
          '/jxct/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/jxct\/api/, ''),
            // mock代理目标地址
            target: 'http://localhost:5320/api',
            ws: true,
          },
        },
      },
    },
  };
});
