<script setup lang="ts">
import { ref } from 'vue';

import { useResizeObserver } from '@vueuse/core';

import { VbenAvatar } from '../avatar';

interface Props {
  /**
   * @zh_CN 是否收起文本
   */
  collapsed?: boolean;
  /**
   * @zh_CN Logo 跳转地址
   */
  href?: string;
  /**
   * @zh_CN Logo 图片大小
   */
  logoSize?: number;
  /**
   * @zh_CN 是否显示文本
   */
  showText?: number;
  /**
   * @zh_CN Logo 图标
   */
  src?: string;
  /**
   * @zh_CN Logo 文本
   */
  text: string;
  /**
   * @zh_CN Logo 主题
   */
  theme?: string;
}

defineOptions({
  name: 'VbenLogo',
});

withDefaults(defineProps<Props>(), {
  collapsed: false,
  href: 'javascript:void 0',
  logoSize: 32,
  src: '',
  theme: 'light',
});
const logoWidth = ref(0);
const logoRef = ref();
const logoInnerWidth = ref(0);
const logoInnerRef = ref();
const showLogo = ref(true);
useResizeObserver(logoRef, (entries) => {
  // 获取并更新组件的宽度
  logoWidth.value = entries[0].contentRect.width;
});
useResizeObserver(logoInnerRef, (entries) => {
  // 获取并更新组件的宽度
  logoInnerWidth.value = entries[0].contentRect.width;
  showLogo.value = logoWidth.value < logoInnerWidth.value;
});
</script>

<template>
  <div ref="logoInnerRef" :class="theme" class="flex items-center text-lg" :style="{ maxWidth: `${logoSize}px` }">
    <a
      :class="$attrs.class"
      :href="href"
      class="flex max-h-full max-w-full items-center gap-2 overflow-hidden p-3 text-lg leading-normal transition-all duration-500"
    >
      <div class="flex-shrink-1">
        <VbenAvatar
          ref="logoRef"
          v-if="src"
          v-show="showLogo"
          :alt="text"
          :src="src"
          class="relative h-full rounded-none bg-transparent"
        />
      </div>
      <span
        v-if="!collapsed && showText !== 0"
        class="text-foreground flex-shrink-0 truncate text-nowrap font-semibold"
      >
        {{ text }}
      </span>
    </a>
  </div>
</template>
