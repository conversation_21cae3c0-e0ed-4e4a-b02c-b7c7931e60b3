<script setup lang="ts">
import { ref } from 'vue';

import { Upload } from 'ant-design-vue';

const iconFileList = ref([]);
const filePath = defineModel('filePath', { type: String });
</script>

<template>
  <Upload v-model:file-list="iconFileList" v-bind="$attrs">
    <img v-if="filePath" :src="filePath" alt="favicon" />
    <div v-else>
      <div>上传</div>
    </div>
  </Upload>
</template>

<style scoped></style>
