<script lang="ts" setup>
import type { CheckboxValueType } from 'ant-design-vue/es/checkbox/interface';

import type { FieldNames } from './props';

import { computed, ref, unref, watch } from 'vue';

import { Checkbox, CheckboxGroup } from 'ant-design-vue';

import { useAttrs } from '#/hooks/core/useAttrs';

import { checkboxProps } from './props';

defineOptions({ name: 'FeCheckbox', inheritAttrs: false });
const props = defineProps(checkboxProps);
const emit = defineEmits(['update:value', 'change']);
const attrs: any = useAttrs({ excludeDefaultKeys: false });
const innerValue = ref<boolean[] | number[] | string[] | undefined>([]);

const getBindValue = computed(() => ({
  ...unref(attrs),
  class: unref(attrs).class ? `fe-${props.direction}-checkbox ${unref(attrs).class}` : `fe-${props.direction}-checkbox`,
}));
const getOptions = computed<any[]>(() => props.options);
const getFieldNames = computed((): Required<FieldNames> => {
  const { fieldNames } = props;
  return {
    disabled: 'disabled',
    label: 'fullName',
    value: 'id',
    ...fieldNames,
  };
});

watch(
  () => props.value,
  (val) => {
    setValue(val);
  },
  { immediate: true },
);

function setValue(value: boolean[] | number[] | string[] | undefined) {
  innerValue.value = value;
}
function onChange(val: CheckboxValueType[]) {
  let list: any[] = [];
  for (const element of val) {
    const item = unref(getOptions).filter((o) => o[unref(getFieldNames).value] === element);
    list = [...list, ...item];
  }
  emit('update:value', val);
  emit('change', val, list);
}
</script>

<template>
  <CheckboxGroup button-style="solid" v-bind="getBindValue" v-model:value="innerValue" @change="onChange">
    <Checkbox
      v-for="item in getOptions"
      :key="item[getFieldNames.value]"
      :value="item[getFieldNames.value]"
      :disabled="item[getFieldNames.disabled]"
    >
      {{ item[getFieldNames.label] }}
    </Checkbox>
  </CheckboxGroup>
</template>
<style lang="less">
.fe-vertical-checkbox {
  .ant-checkbox-wrapper {
    width: 100%;
  }
}
</style>
