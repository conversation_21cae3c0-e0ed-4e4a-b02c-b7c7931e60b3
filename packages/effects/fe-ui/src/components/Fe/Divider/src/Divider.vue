<script lang="ts" setup>
import type { PropType } from 'vue';

import { computed, unref } from 'vue';

import { Divider } from 'ant-design-vue';

import { useAttrs } from '#/hooks/core/useAttrs';

defineOptions({ name: '<PERSON><PERSON><PERSON><PERSON>', inheritAttrs: false });
defineProps({
  content: {
    type: String,
    default: '',
  },
  contentPosition: {
    type: String as PropType<'center' | 'left' | 'right'>,
    default: 'left',
  },
});
const attrs = useAttrs({ excludeDefaultKeys: false });

const getBindValue = computed(() => ({ ...unref(attrs) }));
</script>

<template>
  <Divider :orientation="contentPosition" v-bind="getBindValue">{{ content }}</Divider>
</template>
