<script lang="ts">
import { defineComponent } from 'vue';

import { Button } from 'ant-design-vue';

import { basicProps } from '../props';

export default defineComponent({
  name: 'BasicModalFooter',
  components: { But<PERSON> },
  props: basicProps,
  emits: ['ok', 'continue', 'cancel'],
  setup(_, { emit }) {
    function handleOk(e: Event) {
      emit('ok', e);
    }
    function handleContinue(e: Event) {
      emit('continue', e);
    }
    function handleCancel(e: Event) {
      emit('cancel', e);
    }

    return { handleOk, handleContinue, handleCancel };
  },
});
</script>
<template>
  <div>
    <slot name="insertFooter"></slot>
    <Button
      :type="continueType"
      @click="handleContinue"
      :loading="continueLoading"
      :disabled="confirmLoading"
      v-bind="continueButtonProps"
      v-if="showContinueBtn"
    >
      {{ continueText }}
    </Button>
    <Button v-bind="cancelButtonProps" @click="handleCancel" v-if="showCancelBtn">
      {{ cancelText }}
    </Button>
    <slot name="centerFooter"></slot>
    <Button
      :type="okType"
      @click="handleOk"
      :loading="confirmLoading"
      v-bind="okButtonProps"
      :disabled="okButtonProps?.disabled || continueLoading"
      v-if="showOkBtn"
    >
      {{ okText }}
    </Button>
    <slot name="appendFooter"></slot>
  </div>
</template>
